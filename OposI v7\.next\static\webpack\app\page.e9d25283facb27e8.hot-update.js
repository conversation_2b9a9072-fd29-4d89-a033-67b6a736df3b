"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/dashboardService.ts":
/*!**********************************************!*\
  !*** ./src/lib/supabase/dashboardService.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   obtenerEstadisticasDashboard: () => (/* binding */ obtenerEstadisticasDashboard),\n/* harmony export */   obtenerProximasFlashcards: () => (/* binding */ obtenerProximasFlashcards)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n/* harmony import */ var _flashcardsService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./flashcardsService */ \"(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\");\n/* harmony import */ var _testsService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./testsService */ \"(app-pages-browser)/./src/lib/supabase/testsService.ts\");\n/* harmony import */ var _estadisticasService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./estadisticasService */ \"(app-pages-browser)/./src/lib/supabase/estadisticasService.ts\");\n\n\n\n\n\n/**\n * Obtiene estadísticas generales para el dashboard\n */ async function obtenerEstadisticasDashboard() {\n    try {\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            throw new Error('Usuario no autenticado');\n        }\n        // Obtener documentos\n        const { data: documentos } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('documentos').select('id').eq('user_id', user.id);\n        // Obtener colecciones de flashcards\n        const colecciones = await (0,_flashcardsService__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n        // Obtener tests\n        const tests = await (0,_testsService__WEBPACK_IMPORTED_MODULE_3__.obtenerTests)();\n        // Obtener estadísticas de tests\n        const estadisticasTests = await (0,_testsService__WEBPACK_IMPORTED_MODULE_3__.obtenerEstadisticasGeneralesTests)();\n        // Calcular estadísticas de flashcards\n        let totalFlashcards = 0;\n        let flashcardsParaHoy = 0;\n        let flashcardsNuevas = 0;\n        let flashcardsAprendiendo = 0;\n        let flashcardsRepasando = 0;\n        const coleccionesConEstadisticas = await Promise.all(colecciones.map(async (coleccion)=>{\n            const stats = await (0,_estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerEstadisticasColeccion)(coleccion.id);\n            totalFlashcards += stats.total;\n            flashcardsParaHoy += stats.paraHoy;\n            flashcardsNuevas += stats.nuevas;\n            flashcardsAprendiendo += stats.aprendiendo;\n            flashcardsRepasando += stats.repasando;\n            return {\n                id: coleccion.id,\n                titulo: coleccion.titulo,\n                fechaCreacion: coleccion.creado_en,\n                paraHoy: stats.paraHoy\n            };\n        }));\n        // Ordenar colecciones por fecha de creación (más recientes primero)\n        const coleccionesRecientes = coleccionesConEstadisticas.sort((a, b)=>new Date(b.fechaCreacion).getTime() - new Date(a.fechaCreacion).getTime()).slice(0, 5);\n        // Preparar tests recientes\n        const testsRecientes = tests.map((test)=>({\n                id: test.id,\n                titulo: test.titulo,\n                fechaCreacion: test.creado_en,\n                numeroPreguntas: test.numero_preguntas || 0\n            })).slice(0, 5);\n        return {\n            totalDocumentos: (documentos === null || documentos === void 0 ? void 0 : documentos.length) || 0,\n            totalColeccionesFlashcards: colecciones.length,\n            totalTests: tests.length,\n            totalFlashcards,\n            flashcardsParaHoy,\n            flashcardsNuevas,\n            flashcardsAprendiendo,\n            flashcardsRepasando,\n            testsRealizados: estadisticasTests.totalTests,\n            porcentajeAcierto: estadisticasTests.porcentajeAcierto,\n            coleccionesRecientes,\n            testsRecientes\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas del dashboard:', error);\n        return {\n            totalDocumentos: 0,\n            totalColeccionesFlashcards: 0,\n            totalTests: 0,\n            totalFlashcards: 0,\n            flashcardsParaHoy: 0,\n            flashcardsNuevas: 0,\n            flashcardsAprendiendo: 0,\n            flashcardsRepasando: 0,\n            testsRealizados: 0,\n            porcentajeAcierto: 0,\n            coleccionesRecientes: [],\n            testsRecientes: []\n        };\n    }\n}\n/**\n * Obtiene las próximas flashcards a repasar\n */ async function obtenerProximasFlashcards() {\n    let limite = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n    try {\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            return [];\n        }\n        // Obtener colecciones del usuario\n        const colecciones = await (0,_flashcardsService__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n        if (colecciones.length === 0) {\n            return [];\n        }\n        const hoy = new Date();\n        hoy.setHours(23, 59, 59, 999); // Final del día\n        // Obtener flashcards con progreso que deben estudiarse hoy\n        const { data: progresosHoy, error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select(\"\\n        flashcard_id,\\n        proxima_revision,\\n        estado,\\n        flashcards!inner(\\n          id,\\n          pregunta,\\n          coleccion_id\\n        )\\n      \").lte('proxima_revision', hoy.toISOString()).order('proxima_revision', {\n            ascending: true\n        }).limit(limite);\n        if (errorProgreso) {\n            console.error('Error al obtener progreso de flashcards:', errorProgreso);\n            return [];\n        }\n        // Mapear los resultados con información de la colección\n        const resultado = [];\n        for (const progreso of progresosHoy || []){\n            const flashcard = Array.isArray(progreso.flashcards) ? progreso.flashcards[0] : progreso.flashcards;\n            if (flashcard) {\n                const coleccion = colecciones.find((c)=>c.id === flashcard.coleccion_id);\n                if (coleccion) {\n                    resultado.push({\n                        id: flashcard.id,\n                        pregunta: flashcard.pregunta,\n                        coleccionTitulo: coleccion.titulo,\n                        coleccionId: coleccion.id,\n                        proximaRevision: progreso.proxima_revision,\n                        estado: progreso.estado || 'nuevo'\n                    });\n                }\n            }\n        }\n        return resultado;\n    } catch (error) {\n        console.error('Error al obtener próximas flashcards:', error);\n        return [];\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/dashboardService.ts\n"));

/***/ })

});