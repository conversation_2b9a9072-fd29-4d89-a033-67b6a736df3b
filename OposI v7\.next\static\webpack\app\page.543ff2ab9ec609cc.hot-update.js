"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/dashboardService.ts":
/*!**********************************************!*\
  !*** ./src/lib/supabase/dashboardService.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   obtenerEstadisticasDashboard: () => (/* binding */ obtenerEstadisticasDashboard),\n/* harmony export */   obtenerProximasFlashcards: () => (/* binding */ obtenerProximasFlashcards)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n/* harmony import */ var _flashcardsService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./flashcardsService */ \"(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\");\n/* harmony import */ var _testsService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./testsService */ \"(app-pages-browser)/./src/lib/supabase/testsService.ts\");\n/* harmony import */ var _estadisticasService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./estadisticasService */ \"(app-pages-browser)/./src/lib/supabase/estadisticasService.ts\");\n\n\n\n\n\n/**\n * Obtiene estadísticas generales para el dashboard\n */ async function obtenerEstadisticasDashboard() {\n    try {\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            throw new Error('Usuario no autenticado');\n        }\n        // Obtener documentos\n        const { data: documentos } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('documentos').select('id').eq('user_id', user.id);\n        // Obtener colecciones de flashcards\n        const colecciones = await (0,_flashcardsService__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n        // Obtener tests\n        const tests = await (0,_testsService__WEBPACK_IMPORTED_MODULE_3__.obtenerTests)();\n        // Obtener estadísticas de tests\n        const estadisticasTests = await (0,_testsService__WEBPACK_IMPORTED_MODULE_3__.obtenerEstadisticasGeneralesTests)();\n        // Calcular estadísticas de flashcards\n        let totalFlashcards = 0;\n        let flashcardsParaHoy = 0;\n        let flashcardsNuevas = 0;\n        let flashcardsAprendiendo = 0;\n        let flashcardsRepasando = 0;\n        const coleccionesConEstadisticas = await Promise.all(colecciones.map(async (coleccion)=>{\n            const stats = await (0,_estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerEstadisticasColeccion)(coleccion.id);\n            totalFlashcards += stats.total;\n            flashcardsParaHoy += stats.paraHoy;\n            flashcardsNuevas += stats.nuevas;\n            flashcardsAprendiendo += stats.aprendiendo;\n            flashcardsRepasando += stats.repasando;\n            return {\n                id: coleccion.id,\n                titulo: coleccion.titulo,\n                fechaCreacion: coleccion.creado_en,\n                paraHoy: stats.paraHoy\n            };\n        }));\n        // Ordenar colecciones por fecha de creación (más recientes primero)\n        const coleccionesRecientes = coleccionesConEstadisticas.sort((a, b)=>new Date(b.fechaCreacion).getTime() - new Date(a.fechaCreacion).getTime()).slice(0, 5);\n        // Preparar tests recientes\n        const testsRecientes = tests.map((test)=>({\n                id: test.id,\n                titulo: test.titulo,\n                fechaCreacion: test.creado_en,\n                numeroPreguntas: test.numero_preguntas || 0\n            })).slice(0, 5);\n        return {\n            totalDocumentos: (documentos === null || documentos === void 0 ? void 0 : documentos.length) || 0,\n            totalColeccionesFlashcards: colecciones.length,\n            totalTests: tests.length,\n            totalFlashcards,\n            flashcardsParaHoy,\n            flashcardsNuevas,\n            flashcardsAprendiendo,\n            flashcardsRepasando,\n            testsRealizados: estadisticasTests.totalTests,\n            porcentajeAcierto: estadisticasTests.porcentajeAcierto,\n            coleccionesRecientes,\n            testsRecientes\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas del dashboard:', error);\n        return {\n            totalDocumentos: 0,\n            totalColeccionesFlashcards: 0,\n            totalTests: 0,\n            totalFlashcards: 0,\n            flashcardsParaHoy: 0,\n            flashcardsNuevas: 0,\n            flashcardsAprendiendo: 0,\n            flashcardsRepasando: 0,\n            testsRealizados: 0,\n            porcentajeAcierto: 0,\n            coleccionesRecientes: [],\n            testsRecientes: []\n        };\n    }\n}\n/**\n * Obtiene las próximas flashcards a repasar\n */ async function obtenerProximasFlashcards() {\n    let limite = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n    try {\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            return [];\n        }\n        // Obtener colecciones del usuario\n        const colecciones = await (0,_flashcardsService__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n        if (colecciones.length === 0) {\n            return [];\n        }\n        const hoy = new Date();\n        hoy.setHours(23, 59, 59, 999); // Final del día\n        // Obtener flashcards con progreso que deben estudiarse hoy\n        const { data: progresosHoy, error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select(\"\\n        flashcard_id,\\n        proxima_revision,\\n        estado,\\n        flashcards!inner(\\n          id,\\n          pregunta,\\n          coleccion_id\\n        )\\n      \").lte('proxima_revision', hoy.toISOString()).order('proxima_revision', {\n            ascending: true\n        }).limit(limite);\n        if (errorProgreso) {\n            console.error('Error al obtener progreso de flashcards:', errorProgreso);\n            return [];\n        }\n        // Mapear los resultados con información de la colección\n        const resultado = [];\n        for (const progreso of progresosHoy || []){\n            const flashcard = progreso.flashcards;\n            const coleccion = colecciones.find((c)=>c.id === flashcard.coleccion_id);\n            if (coleccion) {\n                resultado.push({\n                    id: flashcard.id,\n                    pregunta: flashcard.pregunta,\n                    coleccionTitulo: coleccion.titulo,\n                    coleccionId: coleccion.id,\n                    proximaRevision: progreso.proxima_revision,\n                    estado: progreso.estado || 'nuevo'\n                });\n            }\n        }\n        return resultado;\n    } catch (error) {\n        console.error('Error al obtener próximas flashcards:', error);\n        return [];\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/dashboardService.ts\n"));

/***/ })

});