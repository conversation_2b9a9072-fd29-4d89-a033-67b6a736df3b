import { model, prepararDocumentos } from './geminiClient';
import { PROMPT_MAPAS_MENTALES } from '@/config/prompts';

/**
 * Genera un mapa mental a partir de los documentos
 */
export async function generarMapaMental(
  documentos: { titulo: string; contenido: string; categoria?: string; numero_tema?: number }[],
  instrucciones?: string
): Promise<any> {
  try {
    // Validar entrada
    if (!documentos || documentos.length === 0) {
      throw new Error("No se han proporcionado documentos para generar el mapa mental.");
    }

    // Preparar el contenido de los documentos
    const contenidoDocumentos = prepararDocumentos(documentos);

    if (!contenidoDocumentos || contenidoDocumentos.trim().length === 0) {
      throw new Error("El contenido de los documentos está vacío o no es válido.");
    }

    // Validar y limpiar instrucciones
    const instruccionesLimpias = instrucciones?.trim() || 'Crea un mapa mental que organice los conceptos principales del contenido.';

    // Construir el prompt final con validación
    let finalPrompt = PROMPT_MAPAS_MENTALES.replace('{documentos}', contenidoDocumentos);
    finalPrompt = finalPrompt.replace('{instrucciones}', instruccionesLimpias);

    // Generar el mapa mental con timeout
    const result = await model.generateContent(finalPrompt);
    const response = result.response.text();

    // Validar que la respuesta no esté vacía
    if (!response || response.trim().length === 0) {
      throw new Error("La IA no generó ningún contenido para el mapa mental.");
    }

    // Extraer y validar el HTML completo de la respuesta
    let htmlContent = response.trim();

    // Buscar el HTML en la respuesta (puede estar envuelto en markdown)
    const htmlMatch = htmlContent.match(/<!DOCTYPE html>[\s\S]*<\/html>/i);
    if (htmlMatch) {
      htmlContent = htmlMatch[0];
    }

    // Verificar que la respuesta contiene HTML válido
    if (!htmlContent.includes('<!DOCTYPE html>') && !htmlContent.includes('<html')) {
      console.error('Respuesta de Gemini para mapa mental:', response.substring(0, 500) + '...');
      throw new Error("La respuesta no contiene HTML válido para el mapa mental.");
    }

    // Validaciones adicionales de contenido HTML - Más flexibles
    const validationChecks = [
      {
        name: 'SVG',
        check: () => htmlContent.includes('<svg') || htmlContent.includes('svg'),
        patterns: ['<svg', 'svg']
      },
      {
        name: 'D3.js',
        check: () => htmlContent.includes('d3js.org') || htmlContent.includes('d3.v') || htmlContent.includes('d3.min.js') || htmlContent.includes('d3.js'),
        patterns: ['d3js.org', 'd3.v', 'd3.min.js', 'd3.js']
      },
      {
        name: 'JavaScript functions',
        check: () => htmlContent.includes('function') || htmlContent.includes('=>') || htmlContent.includes('const ') || htmlContent.includes('let '),
        patterns: ['function', '=>', 'const ', 'let ']
      },
      {
        name: 'Tree data',
        check: () => htmlContent.includes('treeData') || htmlContent.includes('data') || htmlContent.includes('nodes') || htmlContent.includes('children'),
        patterns: ['treeData', 'data', 'nodes', 'children']
      }
    ];

    const missingElements = validationChecks.filter(check => !check.check());

    if (missingElements.length > 0) {
      console.error('Elementos faltantes en el HTML:', missingElements.map(e => e.name));
      console.error('Patrones buscados:', missingElements.map(e => e.patterns.join(', ')));
      console.error('Contenido HTML generado:', htmlContent.substring(0, 1500) + '...');

      // Solo fallar si faltan elementos críticos (SVG y algún tipo de datos)
      const criticalMissing = missingElements.filter(e => e.name === 'SVG' || e.name === 'Tree data');
      if (criticalMissing.length > 0) {
        throw new Error(`El mapa mental generado no contiene elementos críticos: ${criticalMissing.map(e => e.name).join(', ')}`);
      }

      // Si solo faltan elementos no críticos, mostrar advertencia pero continuar
      console.warn('Advertencia: Algunos elementos opcionales no se encontraron:', missingElements.map(e => e.name).join(', '));
    }

    // Limpiar el HTML de posibles caracteres problemáticos
    htmlContent = htmlContent
      .replace(/```html/gi, '')
      .replace(/```/g, '')
      .trim();

    return htmlContent;
  } catch (error) {
    console.error('Error al generar mapa mental:', error);

    // Proporcionar un mensaje de error más descriptivo si es posible
    if (error instanceof Error) {
      throw new Error(`Error al generar el mapa mental: ${error.message}`);
    }

    throw new Error("Ha ocurrido un error inesperado al generar el mapa mental.");
  }
}
