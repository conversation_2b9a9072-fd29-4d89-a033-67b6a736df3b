"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Dashboard.tsx":
/*!**************************************!*\
  !*** ./src/components/Dashboard.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiBookOpen,FiCalendar,FiCheckSquare,FiClock,FiFileText,FiLayers,FiPlay,FiPlus,FiTarget,FiTrendingUp!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst Dashboard = (param)=>{\n    let { onNavigateToTab } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [proximasFlashcards, setProximasFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            cargarDatos();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const cargarDatos = async ()=>{\n        setIsLoading(true);\n        try {\n            // Por ahora, usar datos de ejemplo hasta que se resuelvan las importaciones\n            const statsData = {\n                totalDocumentos: 5,\n                totalColeccionesFlashcards: 3,\n                totalTests: 2,\n                totalFlashcards: 45,\n                flashcardsParaHoy: 12,\n                flashcardsNuevas: 8,\n                flashcardsAprendiendo: 15,\n                flashcardsRepasando: 22,\n                testsRealizados: 2,\n                porcentajeAcierto: 85.5,\n                coleccionesRecientes: [\n                    {\n                        id: '1',\n                        titulo: 'Constitución Española',\n                        fechaCreacion: '2024-01-15',\n                        paraHoy: 5\n                    },\n                    {\n                        id: '2',\n                        titulo: 'Derecho Administrativo',\n                        fechaCreacion: '2024-01-10',\n                        paraHoy: 7\n                    }\n                ],\n                testsRecientes: [\n                    {\n                        id: '1',\n                        titulo: 'Test Constitución',\n                        fechaCreacion: '2024-01-12',\n                        numeroPreguntas: 20\n                    },\n                    {\n                        id: '2',\n                        titulo: 'Test Administrativo',\n                        fechaCreacion: '2024-01-08',\n                        numeroPreguntas: 15\n                    }\n                ]\n            };\n            const proximasData = [\n                {\n                    id: '1',\n                    pregunta: '¿Cuál es el artículo 1 de la Constitución?',\n                    coleccionTitulo: 'Constitución',\n                    coleccionId: '1',\n                    proximaRevision: '2024-01-20',\n                    estado: 'repasando'\n                },\n                {\n                    id: '2',\n                    pregunta: '¿Qué es el procedimiento administrativo?',\n                    coleccionTitulo: 'Derecho Admin',\n                    coleccionId: '2',\n                    proximaRevision: '2024-01-20',\n                    estado: 'aprendiendo'\n                }\n            ];\n            setEstadisticas(statsData);\n            setProximasFlashcards(proximasData);\n        } catch (error) {\n            console.error('Error al cargar datos del dashboard:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const obtenerSaludo = ()=>{\n        const hora = new Date().getHours();\n        if (hora < 12) return 'Buenos días';\n        if (hora < 18) return 'Buenas tardes';\n        return 'Buenas noches';\n    };\n    const obtenerNombreUsuario = ()=>{\n        var _user_email;\n        return (user === null || user === void 0 ? void 0 : (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]) || 'Estudiante';\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2\",\n                        children: [\n                            obtenerSaludo(),\n                            \", \",\n                            obtenerNombreUsuario(),\n                            \"! \\uD83D\\uDC4B\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-100\",\n                        children: \"\\xbfListo para continuar con tu preparaci\\xf3n? Aqu\\xed tienes un resumen de tu progreso.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Documentos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalDocumentos) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiFileText, {\n                                    className: \"h-8 w-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Colecciones\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalColeccionesFlashcards) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiBook, {\n                                    className: \"h-8 w-8 text-emerald-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Tests\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalTests) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiCheckSquare, {\n                                    className: \"h-8 w-8 text-pink-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Flashcards\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalFlashcards) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiTarget, {\n                                    className: \"h-8 w-8 text-orange-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900\",\n                                children: \"Estudio de Hoy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiCalendar, {\n                                className: \"h-6 w-6 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 rounded-lg p-4 border border-orange-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-orange-800\",\n                                                    children: \"Para Repasar Hoy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-orange-600\",\n                                                    children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.flashcardsParaHoy) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiClock, {\n                                            className: \"h-6 w-6 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-lg p-4 border border-blue-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: \"Nuevas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.flashcardsNuevas) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiBookOpen, {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 rounded-lg p-4 border border-green-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-800\",\n                                                    children: \"% Acierto Tests\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: [\n                                                        (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.porcentajeAcierto.toFixed(1)) || 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiTrendingUp, {\n                                            className: \"h-6 w-6 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined),\n                    estadisticas && estadisticas.flashcardsParaHoy > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onNavigateToTab('misFlashcards'),\n                            className: \"bg-orange-600 hover:bg-orange-700 text-white font-semibold py-2 px-4 rounded-lg flex items-center transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiPlay, {\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Comenzar Estudio\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, undefined),\n            proximasFlashcards.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                        children: \"Pr\\xf3ximas Flashcards\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: proximasFlashcards.slice(0, 3).map((flashcard)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 truncate\",\n                                                children: flashcard.pregunta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: flashcard.coleccionTitulo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(flashcard.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' : flashcard.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' : flashcard.estado === 'repasando' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'),\n                                            children: flashcard.estado\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, flashcard.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, undefined),\n                    proximasFlashcards.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onNavigateToTab('misFlashcards'),\n                        className: \"mt-3 text-blue-600 hover:text-blue-800 text-sm font-medium\",\n                        children: \"Ver todas las flashcards pendientes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 200,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900 mb-4\",\n                                children: \"Colecciones Recientes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.coleccionesRecientes.map((coleccion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: coleccion.titulo\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"Creada: \",\n                                                            new Date(coleccion.fechaCreacion).toLocaleDateString('es-ES')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800\",\n                                                    children: [\n                                                        coleccion.paraHoy,\n                                                        \" para hoy\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, coleccion.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('misFlashcards'),\n                                className: \"mt-3 text-emerald-600 hover:text-emerald-800 text-sm font-medium\",\n                                children: \"Ver todas las colecciones\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900 mb-4\",\n                                children: \"Tests Recientes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.testsRecientes.map((test)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: test.titulo\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"Creado: \",\n                                                            new Date(test.fechaCreacion).toLocaleDateString('es-ES')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800\",\n                                                    children: [\n                                                        test.numeroPreguntas,\n                                                        \" preguntas\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, test.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('misTests'),\n                                className: \"mt-3 text-pink-600 hover:text-pink-800 text-sm font-medium\",\n                                children: \"Ver todos los tests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                        children: \"Acciones R\\xe1pidas\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('preguntas'),\n                                className: \"flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors border border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiFileText, {\n                                        className: \"h-6 w-6 text-blue-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-blue-900\",\n                                        children: \"Hacer Preguntas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('flashcards'),\n                                className: \"flex items-center p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors border border-orange-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiPlus, {\n                                        className: \"h-6 w-6 text-orange-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-orange-900\",\n                                        children: \"Crear Flashcards\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('tests'),\n                                className: \"flex items-center p-4 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors border border-indigo-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiCheckSquare, {\n                                        className: \"h-6 w-6 text-indigo-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-indigo-900\",\n                                        children: \"Generar Tests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('mapas'),\n                                className: \"flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors border border-purple-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiLayers, {\n                                        className: \"h-6 w-6 text-purple-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-purple-900\",\n                                        children: \"Mapas Mentales\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\Dashboard.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Dashboard, \"aHdaFWEU/R61Mg+sun4xSp3R2oQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Dashboard.tsx\n"));

/***/ })

});