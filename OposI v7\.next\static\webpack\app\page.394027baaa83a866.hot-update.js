"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/dashboardService.ts":
/*!**********************************************!*\
  !*** ./src/lib/supabase/dashboardService.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   obtenerEstadisticasDashboard: () => (/* binding */ obtenerEstadisticasDashboard),\n/* harmony export */   obtenerProximasFlashcards: () => (/* binding */ obtenerProximasFlashcards)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n/* harmony import */ var _flashcardsService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./flashcardsService */ \"(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\");\n/* harmony import */ var _testsService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./testsService */ \"(app-pages-browser)/./src/lib/supabase/testsService.ts\");\n/* harmony import */ var _estadisticasService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./estadisticasService */ \"(app-pages-browser)/./src/lib/supabase/estadisticasService.ts\");\n\n\n\n\n\n/**\n * Obtiene estadísticas generales para el dashboard\n */ async function obtenerEstadisticasDashboard() {\n    try {\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            throw new Error('Usuario no autenticado');\n        }\n        // Obtener documentos\n        const { data: documentos } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('documentos').select('id').eq('user_id', user.id);\n        // Obtener colecciones de flashcards\n        const colecciones = await (0,_flashcardsService__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n        // Obtener tests\n        const tests = await (0,_testsService__WEBPACK_IMPORTED_MODULE_3__.obtenerTests)();\n        // Obtener estadísticas de tests\n        const estadisticasTests = await (0,_testsService__WEBPACK_IMPORTED_MODULE_3__.obtenerEstadisticasGeneralesTests)();\n        // Calcular estadísticas de flashcards\n        let totalFlashcards = 0;\n        let flashcardsParaHoy = 0;\n        let flashcardsNuevas = 0;\n        let flashcardsAprendiendo = 0;\n        let flashcardsRepasando = 0;\n        const coleccionesConEstadisticas = await Promise.all(colecciones.map(async (coleccion)=>{\n            const stats = await (0,_estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerEstadisticasColeccion)(coleccion.id);\n            totalFlashcards += stats.total;\n            flashcardsParaHoy += stats.paraHoy;\n            flashcardsNuevas += stats.nuevas;\n            flashcardsAprendiendo += stats.aprendiendo;\n            flashcardsRepasando += stats.repasando;\n            return {\n                id: coleccion.id,\n                titulo: coleccion.titulo,\n                fechaCreacion: coleccion.creado_en,\n                paraHoy: stats.paraHoy\n            };\n        }));\n        // Ordenar colecciones por fecha de creación (más recientes primero)\n        const coleccionesRecientes = coleccionesConEstadisticas.sort((a, b)=>new Date(b.fechaCreacion).getTime() - new Date(a.fechaCreacion).getTime()).slice(0, 5);\n        // Preparar tests recientes\n        const testsRecientes = tests.map((test)=>({\n                id: test.id,\n                titulo: test.titulo,\n                fechaCreacion: test.created_at || test.creado_en,\n                numeroPreguntas: test.numero_preguntas || 0\n            })).slice(0, 5);\n        return {\n            totalDocumentos: (documentos === null || documentos === void 0 ? void 0 : documentos.length) || 0,\n            totalColeccionesFlashcards: colecciones.length,\n            totalTests: tests.length,\n            totalFlashcards,\n            flashcardsParaHoy,\n            flashcardsNuevas,\n            flashcardsAprendiendo,\n            flashcardsRepasando,\n            testsRealizados: estadisticasTests.totalTests,\n            porcentajeAcierto: estadisticasTests.porcentajeAcierto,\n            coleccionesRecientes,\n            testsRecientes\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas del dashboard:', error);\n        return {\n            totalDocumentos: 0,\n            totalColeccionesFlashcards: 0,\n            totalTests: 0,\n            totalFlashcards: 0,\n            flashcardsParaHoy: 0,\n            flashcardsNuevas: 0,\n            flashcardsAprendiendo: 0,\n            flashcardsRepasando: 0,\n            testsRealizados: 0,\n            porcentajeAcierto: 0,\n            coleccionesRecientes: [],\n            testsRecientes: []\n        };\n    }\n}\n/**\n * Obtiene las próximas flashcards a repasar\n */ async function obtenerProximasFlashcards() {\n    let limite = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n    try {\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            return [];\n        }\n        const hoy = new Date();\n        hoy.setHours(23, 59, 59, 999); // Final del día\n        const { data: proximasFlashcards, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select(\"\\n        id,\\n        pregunta,\\n        colecciones_flashcards!inner(\\n          id,\\n          titulo,\\n          user_id\\n        ),\\n        progreso_flashcards(\\n          proxima_revision,\\n          estado\\n        )\\n      \").eq('colecciones_flashcards.user_id', user.id).lte('progreso_flashcards.proxima_revision', hoy.toISOString()).order('progreso_flashcards.proxima_revision', {\n            ascending: true\n        }).limit(limite);\n        if (error) {\n            console.error('Error al obtener próximas flashcards:', error);\n            return [];\n        }\n        return (proximasFlashcards === null || proximasFlashcards === void 0 ? void 0 : proximasFlashcards.map((flashcard)=>{\n            var _flashcard_colecciones_flashcards_, _flashcard_colecciones_flashcards_1, _flashcard_progreso_flashcards_, _flashcard_progreso_flashcards, _flashcard_progreso_flashcards_1, _flashcard_progreso_flashcards1;\n            return {\n                id: flashcard.id,\n                pregunta: flashcard.pregunta,\n                coleccionTitulo: ((_flashcard_colecciones_flashcards_ = flashcard.colecciones_flashcards[0]) === null || _flashcard_colecciones_flashcards_ === void 0 ? void 0 : _flashcard_colecciones_flashcards_.titulo) || '',\n                coleccionId: ((_flashcard_colecciones_flashcards_1 = flashcard.colecciones_flashcards[0]) === null || _flashcard_colecciones_flashcards_1 === void 0 ? void 0 : _flashcard_colecciones_flashcards_1.id) || '',\n                proximaRevision: ((_flashcard_progreso_flashcards = flashcard.progreso_flashcards) === null || _flashcard_progreso_flashcards === void 0 ? void 0 : (_flashcard_progreso_flashcards_ = _flashcard_progreso_flashcards[0]) === null || _flashcard_progreso_flashcards_ === void 0 ? void 0 : _flashcard_progreso_flashcards_.proxima_revision) || '',\n                estado: ((_flashcard_progreso_flashcards1 = flashcard.progreso_flashcards) === null || _flashcard_progreso_flashcards1 === void 0 ? void 0 : (_flashcard_progreso_flashcards_1 = _flashcard_progreso_flashcards1[0]) === null || _flashcard_progreso_flashcards_1 === void 0 ? void 0 : _flashcard_progreso_flashcards_1.estado) || 'nuevo'\n            };\n        })) || [];\n    } catch (error) {\n        console.error('Error al obtener próximas flashcards:', error);\n        return [];\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/dashboardService.ts\n"));

/***/ })

});