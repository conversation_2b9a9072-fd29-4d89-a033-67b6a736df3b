import React from 'react';
import { ColeccionFlashcards } from '@/lib/supabase';
import { FiInbox, FiTrash2, FiRefreshCw } from 'react-icons/fi';

interface FlashcardCollectionListProps {
  colecciones: ColeccionFlashcards[];
  coleccionSeleccionada: ColeccionFlashcards | null;
  onSeleccionarColeccion: (coleccion: ColeccionFlashcards) => void;
  onEliminarColeccion: (coleccionId: string) => void;
  isLoading: boolean;
  deletingId?: string | null;
}

const FlashcardCollectionList: React.FC<FlashcardCollectionListProps> = ({
  colecciones,
  coleccionSeleccionada,
  onSeleccionarColeccion,
  onEliminarColeccion,
  isLoading,
  deletingId
}) => {
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (colecciones.length === 0) {
    return (
      <div className="text-center p-8 border-2 border-dashed border-gray-300 rounded-lg">
        <FiInbox className="mx-auto text-6xl text-gray-400 mb-4" />
        <p className="text-gray-500 text-lg">No hay colecciones de flashcards disponibles.</p>
        <p className="text-sm text-gray-400 mt-1">Crea una nueva colección para empezar a estudiar.</p>
      </div>
    );
  }

  const handleEliminarClick = (e: React.MouseEvent, coleccionId: string) => {
    e.stopPropagation(); // Evitar que se seleccione la colección al hacer clic en eliminar
    onEliminarColeccion(coleccionId);
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
      {colecciones.map((coleccion) => (
        <div
          key={coleccion.id}
          className={`border rounded-lg p-4 cursor-pointer transition-colors flex flex-col justify-between ${
            coleccionSeleccionada?.id === coleccion.id
              ? 'border-orange-500 bg-orange-50 shadow-lg'
              : 'border-gray-200 hover:bg-gray-50'
          }`}
          onClick={() => onSeleccionarColeccion(coleccion)}
        >
          <div>
            <h3 className="font-semibold text-lg mb-2">{coleccion.titulo}</h3>
            {coleccion.descripcion && (
              <p className="text-sm text-gray-600 mb-2 break-words">{coleccion.descripcion}</p>
            )}
            <p className="text-sm text-gray-500 mb-1">
              Flashcards: {typeof coleccion.numero_flashcards === 'number' ? coleccion.numero_flashcards : 'N/A'}
            </p>
            <p className="text-xs text-gray-400">
              Creada: {new Date(coleccion.creado_en).toLocaleDateString('es-ES')}
            </p>
          </div>

          <div className="flex justify-between items-center mt-4">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onSeleccionarColeccion(coleccion);
              }}
              className="bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-opacity-50"
            >
              Estudiar
            </button>
            <button
              onClick={(e) => handleEliminarClick(e, coleccion.id)}
              disabled={deletingId === coleccion.id}
              className="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded text-sm flex items-center focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-opacity-50 disabled:opacity-50"
              title="Eliminar colección"
            >
              {deletingId === coleccion.id ? (
                <FiRefreshCw size={14} className="animate-spin mr-2" />
              ) : (
                <FiTrash2 size={14} className="mr-2" />
              )}
              Eliminar
            </button>
          </div>
        </div>
      ))}
    </div>
  );
};

export default FlashcardCollectionList;
