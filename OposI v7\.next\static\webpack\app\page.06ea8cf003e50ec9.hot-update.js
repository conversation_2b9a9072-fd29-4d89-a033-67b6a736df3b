"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/dashboardService.ts":
/*!**********************************************!*\
  !*** ./src/lib/supabase/dashboardService.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   obtenerEstadisticasDashboard: () => (/* binding */ obtenerEstadisticasDashboard),\n/* harmony export */   obtenerProximasFlashcards: () => (/* binding */ obtenerProximasFlashcards)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n/* harmony import */ var _flashcardsService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./flashcardsService */ \"(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\");\n/* harmony import */ var _testsService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./testsService */ \"(app-pages-browser)/./src/lib/supabase/testsService.ts\");\n/* harmony import */ var _estadisticasService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./estadisticasService */ \"(app-pages-browser)/./src/lib/supabase/estadisticasService.ts\");\n\n\n\n\n\n/**\n * Obtiene estadísticas generales para el dashboard\n */ async function obtenerEstadisticasDashboard() {\n    try {\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            throw new Error('Usuario no autenticado');\n        }\n        // Obtener documentos\n        const { data: documentos } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('documentos').select('id').eq('user_id', user.id);\n        // Obtener colecciones de flashcards\n        const colecciones = await (0,_flashcardsService__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n        // Obtener tests\n        const tests = await (0,_testsService__WEBPACK_IMPORTED_MODULE_3__.obtenerTests)();\n        // Obtener estadísticas de tests\n        const estadisticasTests = await (0,_testsService__WEBPACK_IMPORTED_MODULE_3__.obtenerEstadisticasGeneralesTests)();\n        // Calcular estadísticas de flashcards\n        let totalFlashcards = 0;\n        let flashcardsParaHoy = 0;\n        let flashcardsNuevas = 0;\n        let flashcardsAprendiendo = 0;\n        let flashcardsRepasando = 0;\n        const coleccionesConEstadisticas = await Promise.all(colecciones.map(async (coleccion)=>{\n            const stats = await (0,_estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerEstadisticasColeccion)(coleccion.id);\n            totalFlashcards += stats.total;\n            flashcardsParaHoy += stats.paraHoy;\n            flashcardsNuevas += stats.nuevas;\n            flashcardsAprendiendo += stats.aprendiendo;\n            flashcardsRepasando += stats.repasando;\n            return {\n                id: coleccion.id,\n                titulo: coleccion.titulo,\n                fechaCreacion: coleccion.creado_en,\n                paraHoy: stats.paraHoy\n            };\n        }));\n        // Ordenar colecciones por fecha de creación (más recientes primero)\n        const coleccionesRecientes = coleccionesConEstadisticas.sort((a, b)=>new Date(b.fechaCreacion).getTime() - new Date(a.fechaCreacion).getTime()).slice(0, 5);\n        // Preparar tests recientes\n        const testsRecientes = tests.map((test)=>({\n                id: test.id,\n                titulo: test.titulo,\n                fechaCreacion: test.creado_en,\n                numeroPreguntas: test.numero_preguntas || 0\n            })).slice(0, 5);\n        return {\n            totalDocumentos: (documentos === null || documentos === void 0 ? void 0 : documentos.length) || 0,\n            totalColeccionesFlashcards: colecciones.length,\n            totalTests: tests.length,\n            totalFlashcards,\n            flashcardsParaHoy,\n            flashcardsNuevas,\n            flashcardsAprendiendo,\n            flashcardsRepasando,\n            testsRealizados: estadisticasTests.totalTests,\n            porcentajeAcierto: estadisticasTests.porcentajeAcierto,\n            coleccionesRecientes,\n            testsRecientes\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas del dashboard:', error);\n        return {\n            totalDocumentos: 0,\n            totalColeccionesFlashcards: 0,\n            totalTests: 0,\n            totalFlashcards: 0,\n            flashcardsParaHoy: 0,\n            flashcardsNuevas: 0,\n            flashcardsAprendiendo: 0,\n            flashcardsRepasando: 0,\n            testsRealizados: 0,\n            porcentajeAcierto: 0,\n            coleccionesRecientes: [],\n            testsRecientes: []\n        };\n    }\n}\n/**\n * Obtiene las próximas flashcards a repasar\n */ async function obtenerProximasFlashcards() {\n    let limite = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n    try {\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            return [];\n        }\n        // Obtener colecciones del usuario\n        const colecciones = await (0,_flashcardsService__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n        if (colecciones.length === 0) {\n            return [];\n        }\n        const hoy = new Date();\n        hoy.setHours(23, 59, 59, 999); // Final del día\n        // Obtener progreso de flashcards que deben estudiarse hoy\n        const { data: progresosHoy, error: errorProgreso } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('progreso_flashcards').select('flashcard_id, proxima_revision, estado').lte('proxima_revision', hoy.toISOString()).order('proxima_revision', {\n            ascending: true\n        }).limit(limite);\n        if (errorProgreso) {\n            console.error('Error al obtener progreso de flashcards:', errorProgreso);\n            return [];\n        }\n        if (!progresosHoy || progresosHoy.length === 0) {\n            return [];\n        }\n        // Obtener las flashcards correspondientes\n        const flashcardIds = progresosHoy.map((p)=>p.flashcard_id);\n        const { data: flashcards, error: errorFlashcards } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select('id, pregunta, coleccion_id').in('id', flashcardIds);\n        if (errorFlashcards) {\n            console.error('Error al obtener flashcards:', errorFlashcards);\n            return [];\n        }\n        // Mapear los resultados\n        const resultado = [];\n        for (const progreso of progresosHoy){\n            const flashcard = flashcards === null || flashcards === void 0 ? void 0 : flashcards.find((f)=>f.id === progreso.flashcard_id);\n            if (flashcard) {\n                const coleccion = colecciones.find((c)=>c.id === flashcard.coleccion_id);\n                if (coleccion) {\n                    resultado.push({\n                        id: flashcard.id,\n                        pregunta: flashcard.pregunta,\n                        coleccionTitulo: coleccion.titulo,\n                        coleccionId: coleccion.id,\n                        proximaRevision: progreso.proxima_revision,\n                        estado: progreso.estado || 'nuevo'\n                    });\n                }\n            }\n        }\n        return resultado;\n    } catch (error) {\n        console.error('Error al obtener próximas flashcards:', error);\n        return [];\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/dashboardService.ts\n"));

/***/ })

});