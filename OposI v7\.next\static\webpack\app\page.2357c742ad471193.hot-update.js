"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/supabase/dashboardService.ts":
/*!**********************************************!*\
  !*** ./src/lib/supabase/dashboardService.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   obtenerEstadisticasDashboard: () => (/* binding */ obtenerEstadisticasDashboard),\n/* harmony export */   obtenerProximasFlashcards: () => (/* binding */ obtenerProximasFlashcards)\n/* harmony export */ });\n/* harmony import */ var _supabaseClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabaseClient */ \"(app-pages-browser)/./src/lib/supabase/supabaseClient.ts\");\n/* harmony import */ var _authService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./authService */ \"(app-pages-browser)/./src/lib/supabase/authService.ts\");\n/* harmony import */ var _flashcardsService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./flashcardsService */ \"(app-pages-browser)/./src/lib/supabase/flashcardsService.ts\");\n/* harmony import */ var _testsService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./testsService */ \"(app-pages-browser)/./src/lib/supabase/testsService.ts\");\n/* harmony import */ var _estadisticasService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./estadisticasService */ \"(app-pages-browser)/./src/lib/supabase/estadisticasService.ts\");\n\n\n\n\n\n/**\n * Obtiene estadísticas generales para el dashboard\n */ async function obtenerEstadisticasDashboard() {\n    try {\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            throw new Error('Usuario no autenticado');\n        }\n        // Obtener documentos\n        const { data: documentos } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('documentos').select('id').eq('user_id', user.id);\n        // Obtener colecciones de flashcards\n        const colecciones = await (0,_flashcardsService__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n        // Obtener tests\n        const tests = await (0,_testsService__WEBPACK_IMPORTED_MODULE_3__.obtenerTests)();\n        // Obtener estadísticas de tests\n        const estadisticasTests = await (0,_testsService__WEBPACK_IMPORTED_MODULE_3__.obtenerEstadisticasGeneralesTests)();\n        // Calcular estadísticas de flashcards\n        let totalFlashcards = 0;\n        let flashcardsParaHoy = 0;\n        let flashcardsNuevas = 0;\n        let flashcardsAprendiendo = 0;\n        let flashcardsRepasando = 0;\n        const coleccionesConEstadisticas = await Promise.all(colecciones.map(async (coleccion)=>{\n            const stats = await (0,_estadisticasService__WEBPACK_IMPORTED_MODULE_4__.obtenerEstadisticasColeccion)(coleccion.id);\n            totalFlashcards += stats.total;\n            flashcardsParaHoy += stats.paraHoy;\n            flashcardsNuevas += stats.nuevas;\n            flashcardsAprendiendo += stats.aprendiendo;\n            flashcardsRepasando += stats.repasando;\n            return {\n                id: coleccion.id,\n                titulo: coleccion.titulo,\n                fechaCreacion: coleccion.creado_en,\n                paraHoy: stats.paraHoy\n            };\n        }));\n        // Ordenar colecciones por fecha de creación (más recientes primero)\n        const coleccionesRecientes = coleccionesConEstadisticas.sort((a, b)=>new Date(b.fechaCreacion).getTime() - new Date(a.fechaCreacion).getTime()).slice(0, 5);\n        // Preparar tests recientes\n        const testsRecientes = tests.map((test)=>({\n                id: test.id,\n                titulo: test.titulo,\n                fechaCreacion: test.creado_en,\n                numeroPreguntas: test.numero_preguntas || 0\n            })).slice(0, 5);\n        return {\n            totalDocumentos: (documentos === null || documentos === void 0 ? void 0 : documentos.length) || 0,\n            totalColeccionesFlashcards: colecciones.length,\n            totalTests: tests.length,\n            totalFlashcards,\n            flashcardsParaHoy,\n            flashcardsNuevas,\n            flashcardsAprendiendo,\n            flashcardsRepasando,\n            testsRealizados: estadisticasTests.totalTests,\n            porcentajeAcierto: estadisticasTests.porcentajeAcierto,\n            coleccionesRecientes,\n            testsRecientes\n        };\n    } catch (error) {\n        console.error('Error al obtener estadísticas del dashboard:', error);\n        return {\n            totalDocumentos: 0,\n            totalColeccionesFlashcards: 0,\n            totalTests: 0,\n            totalFlashcards: 0,\n            flashcardsParaHoy: 0,\n            flashcardsNuevas: 0,\n            flashcardsAprendiendo: 0,\n            flashcardsRepasando: 0,\n            testsRealizados: 0,\n            porcentajeAcierto: 0,\n            coleccionesRecientes: [],\n            testsRecientes: []\n        };\n    }\n}\n/**\n * Obtiene las próximas flashcards a repasar\n */ async function obtenerProximasFlashcards() {\n    let limite = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 10;\n    try {\n        const { user } = await (0,_authService__WEBPACK_IMPORTED_MODULE_1__.obtenerUsuarioActual)();\n        if (!user) {\n            return [];\n        }\n        const hoy = new Date();\n        hoy.setHours(23, 59, 59, 999); // Final del día\n        const { data: proximasFlashcards, error } = await _supabaseClient__WEBPACK_IMPORTED_MODULE_0__.supabase.from('flashcards').select(\"\\n        id,\\n        pregunta,\\n        colecciones_flashcards!inner(\\n          id,\\n          titulo,\\n          user_id\\n        ),\\n        progreso_flashcards(\\n          proxima_revision,\\n          estado\\n        )\\n      \").eq('colecciones_flashcards.user_id', user.id).lte('progreso_flashcards.proxima_revision', hoy.toISOString()).order('progreso_flashcards.proxima_revision', {\n            ascending: true\n        }).limit(limite);\n        if (error) {\n            console.error('Error al obtener próximas flashcards:', error);\n            return [];\n        }\n        return (proximasFlashcards === null || proximasFlashcards === void 0 ? void 0 : proximasFlashcards.map((flashcard)=>{\n            var _flashcard_colecciones_flashcards_, _flashcard_colecciones_flashcards_1, _flashcard_progreso_flashcards_, _flashcard_progreso_flashcards, _flashcard_progreso_flashcards_1, _flashcard_progreso_flashcards1;\n            return {\n                id: flashcard.id,\n                pregunta: flashcard.pregunta,\n                coleccionTitulo: ((_flashcard_colecciones_flashcards_ = flashcard.colecciones_flashcards[0]) === null || _flashcard_colecciones_flashcards_ === void 0 ? void 0 : _flashcard_colecciones_flashcards_.titulo) || '',\n                coleccionId: ((_flashcard_colecciones_flashcards_1 = flashcard.colecciones_flashcards[0]) === null || _flashcard_colecciones_flashcards_1 === void 0 ? void 0 : _flashcard_colecciones_flashcards_1.id) || '',\n                proximaRevision: ((_flashcard_progreso_flashcards = flashcard.progreso_flashcards) === null || _flashcard_progreso_flashcards === void 0 ? void 0 : (_flashcard_progreso_flashcards_ = _flashcard_progreso_flashcards[0]) === null || _flashcard_progreso_flashcards_ === void 0 ? void 0 : _flashcard_progreso_flashcards_.proxima_revision) || '',\n                estado: ((_flashcard_progreso_flashcards1 = flashcard.progreso_flashcards) === null || _flashcard_progreso_flashcards1 === void 0 ? void 0 : (_flashcard_progreso_flashcards_1 = _flashcard_progreso_flashcards1[0]) === null || _flashcard_progreso_flashcards_1 === void 0 ? void 0 : _flashcard_progreso_flashcards_1.estado) || 'nuevo'\n            };\n        })) || [];\n    } catch (error) {\n        console.error('Error al obtener próximas flashcards:', error);\n        return [];\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase/dashboardService.ts\n"));

/***/ })

});