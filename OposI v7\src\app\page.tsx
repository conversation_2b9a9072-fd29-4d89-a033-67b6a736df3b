'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { FiBook, FiMessageSquare, FiLayers, FiFileText, FiUpload, FiChevronRight, FiCheck, FiList, FiCheckSquare, FiLogOut, FiRefreshCw, FiSettings } from 'react-icons/fi';
import DocumentSelector, { DocumentSelectorRef } from '../components/DocumentSelector';
import QuestionForm from '../components/QuestionForm';
import DocumentUploader from '../components/DocumentUploader';
import MindMapGenerator from '../components/MindMapGenerator';
import FlashcardGenerator from '../components/FlashcardGenerator';
import DocumentManager from '../components/DocumentManager';
import FlashcardViewer from '../components/flashcards/FlashcardViewer';
import TestGenerator from '../components/TestGenerator';
import TestViewer from '../components/TestViewer';
import Dashboard from '../components/Dashboard';
import MobileDebugInfo from '../components/MobileDebugInfo';
import DiagnosticPanel from '../components/DiagnosticPanel';
import { Documento } from '../lib/supabase';
import { useAuth } from '@/contexts/AuthContext';

type TabType = 'dashboard' | 'preguntas' | 'mapas' | 'flashcards' | 'misFlashcards' | 'tests' | 'misTests' | 'gestionar';

interface TabButtonProps {
  active: boolean;
  onClick: () => void;
  icon: React.ReactNode;
  label: string;
  color: string;
}

const TabButton: React.FC<TabButtonProps> = ({ active, onClick, icon, label, color }) => (
  <button
    onClick={onClick}
    className={`flex items-center px-4 py-3 rounded-lg transition-all duration-200 font-medium text-sm ${
      active
        ? `text-white ${color} shadow-md`
        : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'
    }`}
  >
    <span className="mr-2">{icon}</span>
    {label}
    {active && <FiChevronRight className="ml-2" />}
  </button>
);

export default function Home() {
  const [documentosSeleccionados, setDocumentosSeleccionados] = useState<Documento[]>([]);
  const [mostrarUploader, setMostrarUploader] = useState(false);
  const [activeTab, setActiveTab] = useState<TabType>('dashboard');
  const [showUploadSuccess, setShowUploadSuccess] = useState(false);
  const [isRefreshingDocuments, setIsRefreshingDocuments] = useState(false);
  const { cerrarSesion, user, isLoading } = useAuth();
  const router = useRouter();
  const documentSelectorRef = useRef<DocumentSelectorRef>(null);

  // No necesitamos verificar autenticación aquí, el middleware ya lo hace
  useEffect(() => {
    console.log('[HomePage] Auth state - isLoading:', isLoading, 'User:', !!user);
  }, [user, isLoading]);

  // Si está cargando o no hay usuario, mostrar pantalla de carga
  if (isLoading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando...</p>
        </div>
      </div>
    );
  }

  const handleUploadSuccess = async () => {
    setShowUploadSuccess(true);
    setIsRefreshingDocuments(true);

    // Recargar la lista de documentos automáticamente
    try {
      await documentSelectorRef.current?.recargarDocumentos();
    } catch (error) {
      console.error('Error al recargar documentos:', error);
    } finally {
      setIsRefreshingDocuments(false);
    }

    // Ocultar el mensaje después de 5 segundos
    setTimeout(() => setShowUploadSuccess(false), 5000);
  };

  const handleDocumentDeleted = async () => {
    // Recargar la lista de documentos cuando se elimina uno
    try {
      await documentSelectorRef.current?.recargarDocumentos();
    } catch (error) {
      console.error('Error al recargar documentos después de eliminar:', error);
    }
  };

  const handleLogout = async () => {
    await cerrarSesion();
  };

  const tabs: { id: TabType; label: string; icon: React.ReactNode; color: string }[] = [
    { id: 'dashboard', label: 'Dashboard', icon: <FiRefreshCw />, color: 'bg-gradient-to-r from-blue-600 to-purple-600' },
    { id: 'preguntas', label: 'Preguntas y Respuestas', icon: <FiMessageSquare />, color: 'bg-blue-600' },
    { id: 'mapas', label: 'Mapas Mentales', icon: <FiLayers />, color: 'bg-purple-600' },
    { id: 'flashcards', label: 'Generar Flashcards', icon: <FiFileText />, color: 'bg-orange-500' },
    { id: 'tests', label: 'Generar Tests', icon: <FiList />, color: 'bg-indigo-600' },
    { id: 'misFlashcards', label: 'Mis Flashcards', icon: <FiBook />, color: 'bg-emerald-600' },
    { id: 'misTests', label: 'Mis Tests', icon: <FiCheckSquare />, color: 'bg-pink-600' },
    { id: 'gestionar', label: 'Gestionar Documentos', icon: <FiSettings />, color: 'bg-gray-600' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                OposiAI
              </h1>
              <p className="text-sm text-gray-500">Tu asistente inteligente para oposiciones</p>
            </div>
            <div className="flex items-center space-x-4">
              {user && (
                <div className="text-sm text-gray-600">
                  Hola, {user.email?.split('@')[0]}
                </div>
              )}
              <button
                onClick={() => setMostrarUploader(!mostrarUploader)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
              >
                <FiUpload className="mr-2" />
                {mostrarUploader ? 'Ocultar formulario' : 'Nuevo documento'}
              </button>
              <button
                onClick={handleLogout}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiLogOut className="mr-2" />
                Cerrar sesión
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Uploader */}
        {mostrarUploader && (
          <div className="mb-8 transition-all duration-300 ease-in-out">
            <DocumentUploader onSuccess={handleUploadSuccess} />
          </div>
        )}

        {showUploadSuccess && (
          <div className="mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200">
            <div className="flex items-center">
              <FiCheck className="text-green-500 mr-2 flex-shrink-0" />
              <div>
                <p className="font-medium">¡Documento subido exitosamente!</p>
                <p className="text-sm text-green-700 mt-1">
                  {isRefreshingDocuments ? (
                    <span className="flex items-center">
                      <FiRefreshCw className="animate-spin mr-1" />
                      Actualizando lista de documentos...
                    </span>
                  ) : (
                    "El documento ya está disponible en los desplegables de selección."
                  )}
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm p-4 sticky top-6">
              <h2 className="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-4 px-2">
                Menú de Estudio
              </h2>
              <nav className="space-y-1">
                {tabs.map((tab) => (
                  <TabButton
                    key={tab.id}
                    active={activeTab === tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    icon={tab.icon}
                    label={tab.label}
                    color={tab.color}
                  />
                ))}
              </nav>

              <div className="mt-8 pt-6 border-t border-gray-100">
                <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3 px-2">
                  Documentos Seleccionados
                </h3>
                <DocumentSelector
                  ref={documentSelectorRef}
                  onSelectionChange={setDocumentosSeleccionados}
                />
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {activeTab === 'dashboard' ? (
              <Dashboard onNavigateToTab={setActiveTab} />
            ) : (
              <div className="bg-white rounded-xl shadow-sm overflow-hidden">
                <div className="p-6">
                  {activeTab === 'preguntas' && (
                    <QuestionForm documentosSeleccionados={documentosSeleccionados} />
                  )}

                  {activeTab === 'mapas' && (
                    <MindMapGenerator documentosSeleccionados={documentosSeleccionados} />
                  )}

                  {activeTab === 'flashcards' && (
                    <FlashcardGenerator documentosSeleccionados={documentosSeleccionados} />
                  )}

                  {activeTab === 'tests' && (
                    <TestGenerator documentosSeleccionados={documentosSeleccionados} />
                  )}

                  {activeTab === 'misTests' && <TestViewer />}

                  {activeTab === 'misFlashcards' && <FlashcardViewer />}

                  {activeTab === 'gestionar' && (
                    <DocumentManager onDocumentDeleted={handleDocumentDeleted} />
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center">
              <span className="text-gray-500 text-sm">
                &copy; {new Date().getFullYear()} OposiAI - Asistente para Oposiciones
              </span>
            </div>
            <div className="mt-4 md:mt-0">
              <nav className="flex space-x-6">
                <a href="#" className="text-gray-500 hover:text-gray-700 text-sm">
                  Términos
                </a>
                <a href="#" className="text-gray-500 hover:text-gray-700 text-sm">
                  Privacidad
                </a>
                <a href="#" className="text-gray-500 hover:text-gray-700 text-sm">
                  Contacto
                </a>
              </nav>
            </div>
          </div>
        </div>
      </footer>

      {/* Componente de debug para dispositivos móviles */}
      <MobileDebugInfo />

      {/* Panel de diagnóstico */}
      <DiagnosticPanel />
    </div>
  );
}
