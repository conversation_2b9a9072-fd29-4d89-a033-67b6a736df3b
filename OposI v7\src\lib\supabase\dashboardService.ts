import { supabase } from './supabaseClient';
import { obtenerUsuarioActual } from './authService';
import { obtenerColeccionesFlashcards } from './flashcardsService';
import { obtenerEstadisticasColeccion } from './estadisticasService';
import { obtenerTests, obtenerEstadisticasGeneralesTests } from './testsService';

export interface EstadisticasDashboard {
  // Estadísticas generales
  totalDocumentos: number;
  totalColeccionesFlashcards: number;
  totalTests: number;
  totalFlashcards: number;

  // Flashcards para hoy
  flashcardsParaHoy: number;
  flashcardsNuevas: number;
  flashcardsAprendiendo: number;
  flashcardsRepasando: number;

  // Tests
  testsRealizados: number;
  porcentajeAcierto: number;

  // Actividad reciente
  coleccionesRecientes: {
    id: string;
    titulo: string;
    fechaCreacion: string;
    paraHoy: number;
  }[];

  testsRecientes: {
    id: string;
    titulo: string;
    fechaCreacion: string;
    numeroPreguntas: number;
  }[];
}

export interface ProximasFlashcards {
  id: string;
  pregunta: string;
  coleccionTitulo: string;
  coleccionId: string;
  proximaRevision: string;
  estado: string;
}

/**
 * Obtiene estadísticas generales para el dashboard
 */
export async function obtenerEstadisticasDashboard(): Promise<EstadisticasDashboard> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      throw new Error('Usuario no autenticado');
    }

    // Obtener documentos
    const { data: documentos } = await supabase
      .from('documentos')
      .select('id')
      .eq('user_id', user.id);

    // Obtener colecciones de flashcards
    const colecciones = await obtenerColeccionesFlashcards();

    // Obtener tests
    const tests = await obtenerTests();

    // Obtener estadísticas de tests
    const estadisticasTests = await obtenerEstadisticasGeneralesTests();

    // Calcular estadísticas de flashcards
    let totalFlashcards = 0;
    let flashcardsParaHoy = 0;
    let flashcardsNuevas = 0;
    let flashcardsAprendiendo = 0;
    let flashcardsRepasando = 0;

    const coleccionesConEstadisticas = await Promise.all(
      colecciones.map(async (coleccion) => {
        const stats = await obtenerEstadisticasColeccion(coleccion.id);
        totalFlashcards += stats.total;
        flashcardsParaHoy += stats.paraHoy;
        flashcardsNuevas += stats.nuevas;
        flashcardsAprendiendo += stats.aprendiendo;
        flashcardsRepasando += stats.repasando;

        return {
          id: coleccion.id,
          titulo: coleccion.titulo,
          fechaCreacion: coleccion.creado_en,
          paraHoy: stats.paraHoy
        };
      })
    );

    // Ordenar colecciones por fecha de creación (más recientes primero)
    const coleccionesRecientes = coleccionesConEstadisticas
      .sort((a, b) => new Date(b.fechaCreacion).getTime() - new Date(a.fechaCreacion).getTime())
      .slice(0, 5);

    // Preparar tests recientes
    const testsRecientes = tests
      .map(test => ({
        id: test.id,
        titulo: test.titulo,
        fechaCreacion: test.creado_en,
        numeroPreguntas: test.numero_preguntas || 0
      }))
      .slice(0, 5);

    return {
      totalDocumentos: documentos?.length || 0,
      totalColeccionesFlashcards: colecciones.length,
      totalTests: tests.length,
      totalFlashcards,
      flashcardsParaHoy,
      flashcardsNuevas,
      flashcardsAprendiendo,
      flashcardsRepasando,
      testsRealizados: estadisticasTests.totalTests,
      porcentajeAcierto: estadisticasTests.porcentajeAcierto,
      coleccionesRecientes,
      testsRecientes
    };

  } catch (error) {
    console.error('Error al obtener estadísticas del dashboard:', error);
    return {
      totalDocumentos: 0,
      totalColeccionesFlashcards: 0,
      totalTests: 0,
      totalFlashcards: 0,
      flashcardsParaHoy: 0,
      flashcardsNuevas: 0,
      flashcardsAprendiendo: 0,
      flashcardsRepasando: 0,
      testsRealizados: 0,
      porcentajeAcierto: 0,
      coleccionesRecientes: [],
      testsRecientes: []
    };
  }
}

/**
 * Obtiene las próximas flashcards a repasar
 */
export async function obtenerProximasFlashcards(limite: number = 10): Promise<ProximasFlashcards[]> {
  try {
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      return [];
    }

    const hoy = new Date();
    hoy.setHours(23, 59, 59, 999); // Final del día

    const { data: proximasFlashcards, error } = await supabase
      .from('flashcards')
      .select(`
        id,
        pregunta,
        coleccion_flashcards!inner(
          id,
          titulo,
          user_id
        ),
        progreso_flashcards(
          proxima_revision,
          estado
        )
      `)
      .eq('coleccion_flashcards.user_id', user.id)
      .lte('progreso_flashcards.proxima_revision', hoy.toISOString())
      .order('progreso_flashcards.proxima_revision', { ascending: true })
      .limit(limite);

    if (error) {
      console.error('Error al obtener próximas flashcards:', error);
      return [];
    }

    return proximasFlashcards?.map(flashcard => ({
      id: flashcard.id,
      pregunta: flashcard.pregunta,
      coleccionTitulo: flashcard.coleccion_flashcards.titulo,
      coleccionId: flashcard.coleccion_flashcards.id,
      proximaRevision: flashcard.progreso_flashcards?.[0]?.proxima_revision || '',
      estado: flashcard.progreso_flashcards?.[0]?.estado || 'nuevo'
    })) || [];

  } catch (error) {
    console.error('Error al obtener próximas flashcards:', error);
    return [];
  }
}
