"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/motion/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createRendererMotionComponent: () => (/* binding */ createRendererMotionComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var motion_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! motion-utils */ \"(app-pages-browser)/./node_modules/motion-utils/dist/es/errors.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../context/LayoutGroupContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../context/LazyContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LazyContext.mjs\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../context/MotionContext/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/index.mjs\");\n/* harmony import */ var _context_MotionContext_create_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/MotionContext/create.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/MotionContext/create.mjs\");\n/* harmony import */ var _utils_is_browser_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../utils/is-browser.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/is-browser.mjs\");\n/* harmony import */ var _features_definitions_mjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./features/definitions.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/definitions.mjs\");\n/* harmony import */ var _features_load_features_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./features/load-features.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/features/load-features.mjs\");\n/* harmony import */ var _utils_symbol_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./utils/symbol.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/symbol.mjs\");\n/* harmony import */ var _utils_use_motion_ref_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./utils/use-motion-ref.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs\");\n/* harmony import */ var _utils_use_visual_element_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/use-visual-element.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs\");\n/* __next_internal_client_entry_do_not_use__ createRendererMotionComponent auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Create a `motion` component.\n *\n * This function accepts a Component argument, which can be either a string (ie \"div\"\n * for `motion.div`), or an actual React component.\n *\n * Alongside this is a config option which provides a way of rendering the provided\n * component \"offline\", or outside the React render cycle.\n */ function createRendererMotionComponent(param) {\n    let { preloadedFeatures, createVisualElement, useRender, useVisualState, Component } = param;\n    var _s = $RefreshSig$();\n    preloadedFeatures && (0,_features_load_features_mjs__WEBPACK_IMPORTED_MODULE_2__.loadFeatures)(preloadedFeatures);\n    function MotionComponent(props, externalRef) {\n        _s();\n        /**\n         * If we need to measure the element we load this functionality in a\n         * separate class component in order to gain access to getSnapshotBeforeUpdate.\n         */ let MeasureLayout;\n        const configAndProps = {\n            ...(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext),\n            ...props,\n            layoutId: useLayoutId(props)\n        };\n        const { isStatic } = configAndProps;\n        const context = (0,_context_MotionContext_create_mjs__WEBPACK_IMPORTED_MODULE_4__.useCreateMotionContext)(props);\n        const visualState = useVisualState(props, isStatic);\n        if (!isStatic && _utils_is_browser_mjs__WEBPACK_IMPORTED_MODULE_5__.isBrowser) {\n            useStrictMode(configAndProps, preloadedFeatures);\n            const layoutProjection = getProjectionFunctionality(configAndProps);\n            MeasureLayout = layoutProjection.MeasureLayout;\n            /**\n             * Create a VisualElement for this component. A VisualElement provides a common\n             * interface to renderer-specific APIs (ie DOM/Three.js etc) as well as\n             * providing a way of rendering to these APIs outside of the React render loop\n             * for more performant animations and interactions\n             */ context.visualElement = (0,_utils_use_visual_element_mjs__WEBPACK_IMPORTED_MODULE_6__.useVisualElement)(Component, visualState, configAndProps, createVisualElement, layoutProjection.ProjectionNode);\n        }\n        /**\n         * The mount order and hierarchy is specific to ensure our element ref\n         * is hydrated by the time features fire their effects.\n         */ return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(_context_MotionContext_index_mjs__WEBPACK_IMPORTED_MODULE_7__.MotionContext.Provider, {\n            value: context,\n            children: [\n                MeasureLayout && context.visualElement ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(MeasureLayout, {\n                    visualElement: context.visualElement,\n                    ...configAndProps\n                }) : null,\n                useRender(Component, props, (0,_utils_use_motion_ref_mjs__WEBPACK_IMPORTED_MODULE_8__.useMotionRef)(visualState, context.visualElement, externalRef), visualState, isStatic, context.visualElement)\n            ]\n        });\n    }\n    _s(MotionComponent, \"OzmmWP8E2WLE0LhHHUY21ioDbYk=\", false, function() {\n        return [\n            useLayoutId,\n            _context_MotionContext_create_mjs__WEBPACK_IMPORTED_MODULE_4__.useCreateMotionContext,\n            useVisualState,\n            _utils_use_motion_ref_mjs__WEBPACK_IMPORTED_MODULE_8__.useMotionRef,\n            useRender\n        ];\n    });\n    var _Component_displayName, _ref;\n    MotionComponent.displayName = \"motion.\".concat(typeof Component === \"string\" ? Component : \"create(\".concat((_ref = (_Component_displayName = Component.displayName) !== null && _Component_displayName !== void 0 ? _Component_displayName : Component.name) !== null && _ref !== void 0 ? _ref : \"\", \")\"));\n    const ForwardRefMotionComponent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(MotionComponent);\n    ForwardRefMotionComponent[_utils_symbol_mjs__WEBPACK_IMPORTED_MODULE_9__.motionComponentSymbol] = Component;\n    return ForwardRefMotionComponent;\n}\nfunction useLayoutId(param) {\n    let { layoutId } = param;\n    _s();\n    const layoutGroupId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_10__.LayoutGroupContext).id;\n    return layoutGroupId && layoutId !== undefined ? layoutGroupId + \"-\" + layoutId : layoutId;\n}\n_s(useLayoutId, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nfunction useStrictMode(configAndProps, preloadedFeatures) {\n    _s1();\n    const isStrict = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_LazyContext_mjs__WEBPACK_IMPORTED_MODULE_11__.LazyContext).strict;\n    /**\n     * If we're in development mode, check to make sure we're not rendering a motion component\n     * as a child of LazyMotion, as this will break the file-size benefits of using it.\n     */ if ( true && preloadedFeatures && isStrict) {\n        const strictMessage = \"You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.\";\n        configAndProps.ignoreStrict ? (0,motion_utils__WEBPACK_IMPORTED_MODULE_12__.warning)(false, strictMessage) : (0,motion_utils__WEBPACK_IMPORTED_MODULE_12__.invariant)(false, strictMessage);\n    }\n}\n_s1(useStrictMode, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nfunction getProjectionFunctionality(props) {\n    const { drag, layout } = _features_definitions_mjs__WEBPACK_IMPORTED_MODULE_13__.featureDefinitions;\n    if (!drag && !layout) return {};\n    const combined = {\n        ...drag,\n        ...layout\n    };\n    return {\n        MeasureLayout: (drag === null || drag === void 0 ? void 0 : drag.isEnabled(props)) || (layout === null || layout === void 0 ? void 0 : layout.isEnabled(props)) ? combined.MeasureLayout : undefined,\n        ProjectionNode: combined.ProjectionNode\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/motion/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/TestViewer.tsx":
/*!***************************************!*\
  !*** ./src/components/TestViewer.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestViewer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertTriangle,FiAward,FiBarChart2,FiCheck,FiChevronLeft,FiChevronRight,FiClock,FiPieChart,FiTrendingUp,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\nfunction TestViewer() {\n    var _preguntas_preguntaActual;\n    _s();\n    const [tests, setTests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [testSeleccionado, setTestSeleccionado] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [preguntas, setPreguntas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [preguntaActual, setPreguntaActual] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [respuestaSeleccionada, setRespuestaSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [respuestasUsuario, setRespuestasUsuario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [testCompletado, setTestCompletado] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [resultados, setResultados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tiempoInicio, setTiempoInicio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tiemposPregunta, setTiemposPregunta] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [cargando, setCargando] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [mostrarEstadisticasGenerales, setMostrarEstadisticasGenerales] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarEstadisticasTest, setMostrarEstadisticasTest] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [estadisticasGenerales, setEstadisticasGenerales] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [estadisticasTest, setEstadisticasTest] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Cargar los tests al inicio\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestViewer.useEffect\": ()=>{\n            cargarTests();\n        }\n    }[\"TestViewer.useEffect\"], []);\n    // Cargar las preguntas cuando se selecciona un test\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestViewer.useEffect\": ()=>{\n            if (testSeleccionado) {\n                cargarPreguntas(testSeleccionado.id);\n            }\n        }\n    }[\"TestViewer.useEffect\"], [\n        testSeleccionado\n    ]);\n    // Iniciar el temporizador cuando se cargan las preguntas\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TestViewer.useEffect\": ()=>{\n            if (preguntas.length > 0 && !testCompletado) {\n                setTiempoInicio(Date.now());\n            }\n        }\n    }[\"TestViewer.useEffect\"], [\n        preguntas\n    ]);\n    const cargarTests = async ()=>{\n        setCargando(true);\n        setError('');\n        try {\n            const testsData = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerTests)();\n            // Enrich tests with question counts\n            const testsConConteos = await Promise.all(testsData.map(async (test)=>{\n                try {\n                    const count = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerPreguntasTestCount)(test.id);\n                    return {\n                        ...test,\n                        numero_preguntas: count\n                    };\n                } catch (countError) {\n                    console.error(\"Error al obtener conteo para test \".concat(test.id, \":\"), countError);\n                    return {\n                        ...test,\n                        numero_preguntas: undefined\n                    }; // O algún valor por defecto o manejo de error\n                }\n            }));\n            setTests(testsConConteos);\n        } catch (error) {\n            console.error('Error al cargar tests:', error);\n            setError('No se pudieron cargar los tests. Por favor, inténtalo de nuevo.');\n        } finally{\n            setCargando(false);\n        }\n    };\n    const cargarPreguntas = async (testId)=>{\n        setCargando(true);\n        try {\n            const preguntasData = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerPreguntasPorTestId)(testId);\n            setPreguntas(preguntasData);\n            setPreguntaActual(0);\n            setRespuestaSeleccionada(null);\n            setRespuestasUsuario({});\n            setTestCompletado(false);\n            setResultados(null);\n        } catch (error) {\n            console.error('Error al cargar preguntas:', error);\n            setError('No se pudieron cargar las preguntas del test. Por favor, inténtalo de nuevo.');\n        } finally{\n            setCargando(false);\n        }\n    };\n    const seleccionarRespuesta = (opcion)=>{\n        if (testCompletado) return;\n        setRespuestaSeleccionada(opcion);\n        // Guardar el tiempo que tardó en responder\n        if (tiempoInicio) {\n            const tiempoActual = Date.now();\n            const tiempoPregunta = tiempoActual - tiempoInicio;\n            setTiemposPregunta((prev)=>({\n                    ...prev,\n                    [preguntas[preguntaActual].id]: tiempoPregunta\n                }));\n            setTiempoInicio(tiempoActual);\n        }\n        // Guardar la respuesta del usuario\n        setRespuestasUsuario((prev)=>({\n                ...prev,\n                [preguntas[preguntaActual].id]: opcion\n            }));\n    };\n    const siguientePregunta = async ()=>{\n        if (preguntaActual < preguntas.length - 1) {\n            // Guardar estadística de la pregunta actual\n            await guardarEstadisticaPregunta();\n            // Avanzar a la siguiente pregunta\n            setPreguntaActual(preguntaActual + 1);\n            setRespuestaSeleccionada(null);\n        } else {\n            // Es la última pregunta, finalizar el test\n            await guardarEstadisticaPregunta();\n            finalizarTest();\n        }\n    };\n    const anteriorPregunta = ()=>{\n        if (preguntaActual > 0) {\n            setPreguntaActual(preguntaActual - 1);\n            setRespuestaSeleccionada(respuestasUsuario[preguntas[preguntaActual - 1].id] || null);\n        }\n    };\n    const guardarEstadisticaPregunta = async ()=>{\n        if (!testSeleccionado || !respuestaSeleccionada) return;\n        const preguntaActualObj = preguntas[preguntaActual];\n        const esCorrecta = respuestaSeleccionada === preguntaActualObj.respuesta_correcta;\n        try {\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.registrarRespuestaTest)(testSeleccionado.id, preguntaActualObj.id, respuestaSeleccionada, esCorrecta);\n        } catch (error) {\n            console.error('Error al guardar estadística:', error);\n        }\n    };\n    const finalizarTest = ()=>{\n        setTestCompletado(true);\n        // Calcular resultados\n        let correctas = 0;\n        let incorrectas = 0;\n        let tiempoTotal = 0;\n        preguntas.forEach((pregunta)=>{\n            const respuestaUsuario = respuestasUsuario[pregunta.id];\n            if (respuestaUsuario) {\n                if (respuestaUsuario === pregunta.respuesta_correcta) {\n                    correctas++;\n                } else {\n                    incorrectas++;\n                }\n            } else {\n                incorrectas++;\n            }\n            tiempoTotal += tiemposPregunta[pregunta.id] || 0;\n        });\n        const porcentaje = correctas / preguntas.length * 100;\n        setResultados({\n            correctas,\n            incorrectas,\n            porcentaje,\n            tiempoTotal\n        });\n    };\n    const reiniciarTest = ()=>{\n        setPreguntaActual(0);\n        setRespuestaSeleccionada(null);\n        setRespuestasUsuario({});\n        setTestCompletado(false);\n        setResultados(null);\n        setTiempoInicio(Date.now());\n        setTiemposPregunta({});\n    };\n    const cargarEstadisticasGenerales = async ()=>{\n        try {\n            const estadisticas = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasGeneralesTests)();\n            setEstadisticasGenerales(estadisticas);\n            setMostrarEstadisticasGenerales(true);\n            setMostrarEstadisticasTest(false);\n        } catch (error) {\n            console.error('Error al cargar estadísticas generales:', error);\n            setError('No se pudieron cargar las estadísticas generales. Por favor, inténtalo de nuevo.');\n        }\n    };\n    const cargarEstadisticasTest = async (testId)=>{\n        try {\n            const estadisticas = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasTest)(testId);\n            setEstadisticasTest(estadisticas);\n            setMostrarEstadisticasTest(true);\n            setMostrarEstadisticasGenerales(false);\n        } catch (error) {\n            console.error('Error al cargar estadísticas del test:', error);\n            setError('No se pudieron cargar las estadísticas del test. Por favor, inténtalo de nuevo.');\n        }\n    };\n    const formatTiempo = (ms)=>{\n        const segundos = Math.floor(ms / 1000);\n        const minutos = Math.floor(segundos / 60);\n        const segundosRestantes = segundos % 60;\n        return \"\".concat(minutos, \":\").concat(segundosRestantes.toString().padStart(2, '0'));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                lineNumber: 253,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold\",\n                        children: \"Mis Tests\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: cargarEstadisticasGenerales,\n                        className: \"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded flex items-center focus:outline-none focus:shadow-outline\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiBarChart2, {\n                                className: \"mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this),\n                            \" Estad\\xedsticas Generales\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this),\n            !testSeleccionado && !mostrarEstadisticasGenerales && !mostrarEstadisticasTest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6\",\n                children: tests.map((test)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border rounded-lg p-4 cursor-pointer hover:bg-gray-50 transition-colors flex flex-col justify-between\",\n                        onClick: ()=>setTestSeleccionado(test),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-lg mb-2\",\n                                        children: test.titulo\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this),\n                                    test.descripcion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-2 break-words\",\n                                        children: test.descripcion\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 38\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mb-1\",\n                                        children: [\n                                            \"Preguntas: \",\n                                            typeof test.numero_preguntas === 'number' ? test.numero_preguntas : 'Cargando...'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 280,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: [\n                                            \"Creado: \",\n                                            new Date(test.creado_en).toLocaleDateString('es-ES')\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation(); // Evita que el click en el botón propague al div contenedor\n                                            setTestSeleccionado(test);\n                                        },\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50\",\n                                        children: \"Realizar Test\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: (e)=>{\n                                            e.stopPropagation(); // Evita que el click en el botón propague al div contenedor\n                                            cargarEstadisticasTest(test.id);\n                                        },\n                                        className: \"bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded text-sm flex items-center focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiPieChart, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Estad\\xedsticas\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, test.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                lineNumber: 270,\n                columnNumber: 9\n            }, this),\n            mostrarEstadisticasGenerales && estadisticasGenerales && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border rounded-lg p-6 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold\",\n                                children: \"Estad\\xedsticas Generales\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setMostrarEstadisticasGenerales(false),\n                                className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 py-1 px-3 rounded\",\n                                children: \"Volver\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                        lineNumber: 315,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiAward, {\n                                                className: \"text-blue-600 mr-2 text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold\",\n                                                children: \"Tests Realizados\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-3xl font-bold text-blue-700\",\n                                        children: estadisticasGenerales.totalTests\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiCheck, {\n                                                className: \"text-green-600 mr-2 text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold\",\n                                                children: \"Respuestas Correctas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-3xl font-bold text-green-700\",\n                                        children: estadisticasGenerales.totalRespuestasCorrectas\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiX, {\n                                                className: \"text-red-600 mr-2 text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold\",\n                                                children: \"Respuestas Incorrectas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-3xl font-bold text-red-700\",\n                                        children: estadisticasGenerales.totalRespuestasIncorrectas\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiTrendingUp, {\n                                                className: \"text-purple-600 mr-2 text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold\",\n                                                children: \"Porcentaje de Acierto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-3xl font-bold text-purple-700\",\n                                        children: [\n                                            estadisticasGenerales.porcentajeAcierto.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                lineNumber: 314,\n                columnNumber: 9\n            }, this),\n            mostrarEstadisticasTest && estadisticasTest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border rounded-lg p-6 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold\",\n                                children: \"Estad\\xedsticas del Test\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setMostrarEstadisticasTest(false),\n                                className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 py-1 px-3 rounded\",\n                                children: \"Volver\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiClock, {\n                                                className: \"text-blue-600 mr-2 text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold\",\n                                                children: \"Veces Realizado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-3xl font-bold text-blue-700\",\n                                        children: estadisticasTest.fechasRealizacion.length\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiCheck, {\n                                                className: \"text-green-600 mr-2 text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold\",\n                                                children: \"Respuestas Correctas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-3xl font-bold text-green-700\",\n                                        children: estadisticasTest.totalCorrectas\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiX, {\n                                                className: \"text-red-600 mr-2 text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold\",\n                                                children: \"Respuestas Incorrectas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-3xl font-bold text-red-700\",\n                                        children: estadisticasTest.totalIncorrectas\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-purple-50 border border-purple-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiTrendingUp, {\n                                                className: \"text-purple-600 mr-2 text-xl\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold\",\n                                                children: \"Porcentaje de Acierto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-3xl font-bold text-purple-700\",\n                                        children: [\n                                            estadisticasTest.porcentajeAcierto.toFixed(1),\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                        lineNumber: 374,\n                        columnNumber: 11\n                    }, this),\n                    estadisticasTest.preguntasMasFalladas.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-lg mb-3\",\n                                children: \"Preguntas con m\\xe1s fallos:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: estadisticasTest.preguntasMasFalladas.map((pregunta, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-red-100 text-red-800 rounded-full w-6 h-6 flex items-center justify-center mr-2 flex-shrink-0\",\n                                                    children: index + 1\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                    lineNumber: 415,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: pregunta.pregunta\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center mt-2 text-sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-600 mr-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiX, {\n                                                                            className: \"inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                                            lineNumber: 422,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        pregunta.totalFallos,\n                                                                        \" fallos\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                                    lineNumber: 421,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-600\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiCheck, {\n                                                                            className: \"inline mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                                            lineNumber: 425,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        pregunta.totalAciertos,\n                                                                        \" aciertos\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                            lineNumber: 420,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                    lineNumber: 418,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                            lineNumber: 414,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, pregunta.preguntaId, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                lineNumber: 363,\n                columnNumber: 9\n            }, this),\n            testSeleccionado && preguntas.length > 0 && !mostrarEstadisticasGenerales && !mostrarEstadisticasTest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border rounded-lg shadow-md p-6 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold\",\n                                children: testSeleccionado.titulo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setTestSeleccionado(null),\n                                className: \"bg-gray-200 hover:bg-gray-300 text-gray-800 py-1 px-3 rounded\",\n                                children: \"Volver\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 11\n                    }, this),\n                    testCompletado && resultados && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-50 border rounded-lg p-4 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-semibold text-lg mb-3\",\n                                children: \"Resultados del Test\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-green-50 border border-green-200 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-green-800\",\n                                                children: \"Correctas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-green-700\",\n                                                children: resultados.correctas\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-red-50 border border-red-200 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-red-800\",\n                                                children: \"Incorrectas\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-red-700\",\n                                                children: resultados.incorrectas\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-50 border border-blue-200 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-blue-800\",\n                                                children: \"Porcentaje\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-blue-700\",\n                                                children: [\n                                                    resultados.porcentaje.toFixed(1),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-purple-50 border border-purple-200 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-purple-800\",\n                                                children: \"Tiempo Total\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-purple-700\",\n                                                children: formatTiempo(resultados.tiempoTotal)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 flex justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: reiniciarTest,\n                                    className: \"bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded\",\n                                    children: \"Realizar de nuevo\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 473,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gray-600\",\n                                        children: [\n                                            \"Pregunta \",\n                                            preguntaActual + 1,\n                                            \" de \",\n                                            preguntas.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 487,\n                                        columnNumber: 15\n                                    }, this),\n                                    !testCompletado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiClock, {\n                                                className: \"inline mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 492,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" Tiempo por pregunta\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-2 bg-gray-200 rounded-full mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-2 bg-indigo-600 rounded-full\",\n                                    style: {\n                                        width: \"\".concat((preguntaActual + 1) / preguntas.length * 100, \"%\")\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                        lineNumber: 485,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-[300px]\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold text-lg mb-6\",\n                                    children: (_preguntas_preguntaActual = preguntas[preguntaActual]) === null || _preguntas_preguntaActual === void 0 ? void 0 : _preguntas_preguntaActual.pregunta\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 mt-6\",\n                                    children: [\n                                        'a',\n                                        'b',\n                                        'c',\n                                        'd'\n                                    ].map((opcion)=>{\n                                        var _preguntas_preguntaActual;\n                                        const esCorrecta = testCompletado && opcion === preguntas[preguntaActual].respuesta_correcta;\n                                        const esIncorrecta = testCompletado && respuestaSeleccionada === opcion && opcion !== preguntas[preguntaActual].respuesta_correcta;\n                                        const esSeleccionada = respuestaSeleccionada === opcion;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-3 border rounded-lg cursor-pointer \".concat(esCorrecta ? 'bg-green-100 border-green-500' : esIncorrecta ? 'bg-red-100 border-red-500' : esSeleccionada ? 'bg-indigo-100 border-indigo-500' : 'hover:bg-gray-50'),\n                                            onClick: ()=>!testCompletado && seleccionarRespuesta(opcion),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-2 \".concat(esCorrecta ? 'bg-green-500 text-white' : esIncorrecta ? 'bg-red-500 text-white' : esSeleccionada ? 'bg-indigo-500 text-white' : 'bg-gray-200 text-gray-700'),\n                                                        children: opcion.toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-grow\",\n                                                        children: (_preguntas_preguntaActual = preguntas[preguntaActual]) === null || _preguntas_preguntaActual === void 0 ? void 0 : _preguntas_preguntaActual[\"opcion_\".concat(opcion)]\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, opcion, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                            lineNumber: 515,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between mt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: anteriorPregunta,\n                                disabled: preguntaActual === 0,\n                                className: \"flex items-center \".concat(preguntaActual === 0 ? 'text-gray-400 cursor-not-allowed' : 'text-indigo-600 hover:text-indigo-800'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiChevronLeft, {\n                                        className: \"mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 15\n                                    }, this),\n                                    \" Anterior\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 13\n                            }, this),\n                            !testCompletado && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: siguientePregunta,\n                                disabled: !respuestaSeleccionada,\n                                className: \"bg-indigo-600 text-white py-2 px-4 rounded flex items-center \".concat(!respuestaSeleccionada ? 'opacity-50 cursor-not-allowed' : 'hover:bg-indigo-700'),\n                                children: [\n                                    preguntaActual === preguntas.length - 1 ? 'Finalizar Test' : 'Siguiente',\n                                    preguntaActual < preguntas.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiChevronRight, {\n                                        className: \"ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 59\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 15\n                            }, this),\n                            testCompletado && preguntaActual < preguntas.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: siguientePregunta,\n                                className: \"text-indigo-600 hover:text-indigo-800 flex items-center\",\n                                children: [\n                                    \"Siguiente \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiChevronRight, {\n                                        className: \"ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 27\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 570,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                        lineNumber: 545,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                lineNumber: 440,\n                columnNumber: 9\n            }, this),\n            cargando && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-40\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                    lineNumber: 584,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                lineNumber: 583,\n                columnNumber: 9\n            }, this),\n            !cargando && tests.length === 0 && !mostrarEstadisticasGenerales && !mostrarEstadisticasTest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-50 border border-yellow-200 text-yellow-800 p-4 rounded flex items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiAward_FiBarChart2_FiCheck_FiChevronLeft_FiChevronRight_FiClock_FiPieChart_FiTrendingUp_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiAlertTriangle, {\n                        className: \"mr-2 mt-1 flex-shrink-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-medium\",\n                                children: \"No hay tests disponibles\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 593,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-1\",\n                                children: 'Genera nuevos tests desde la secci\\xf3n \"Generar Tests\".'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                        lineNumber: 592,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n                lineNumber: 590,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\TestViewer.tsx\",\n        lineNumber: 251,\n        columnNumber: 5\n    }, this);\n}\n_s(TestViewer, \"P1TgQcJaHNe+Rwxar4gTfg8Qdkc=\");\n_c = TestViewer;\nvar _c;\n$RefreshReg$(_c, \"TestViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TestViewer.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx":
/*!*******************************************************!*\
  !*** ./src/components/flashcards/FlashcardViewer.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _barrel_optimize_names_FiAlertTriangle_FiBarChart2_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertTriangle,FiBarChart2,FiEdit2,FiRefreshCw,FiTrash2!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./FlashcardCollectionList */ \"(app-pages-browser)/./src/components/flashcards/FlashcardCollectionList.tsx\");\n/* harmony import */ var _FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./FlashcardStatistics */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStatistics.tsx\");\n/* harmony import */ var _FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./FlashcardStudyMode */ \"(app-pages-browser)/./src/components/flashcards/FlashcardStudyMode.tsx\");\n/* harmony import */ var _FlashcardEditModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./FlashcardEditModal */ \"(app-pages-browser)/./src/components/flashcards/FlashcardEditModal.tsx\");\n/* harmony import */ var _FlashcardDetailedStatistics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./FlashcardDetailedStatistics */ \"(app-pages-browser)/./src/components/flashcards/FlashcardDetailedStatistics.tsx\");\n/* harmony import */ var _FlashcardGeneralStatistics__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./FlashcardGeneralStatistics */ \"(app-pages-browser)/./src/components/flashcards/FlashcardGeneralStatistics.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst FlashcardViewer = ()=>{\n    _s();\n    // Estado para las colecciones\n    const [colecciones, setColecciones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coleccionSeleccionada, setColeccionSeleccionada] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para las flashcards\n    const [flashcards, setFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [mostrarRespuesta, setMostrarRespuesta] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [respondiendo, setRespondiendo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para el modo de estudio\n    const [modoEstudio, setModoEstudio] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Estado para estadísticas\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para carga y errores\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Estado para edición y eliminación de flashcards\n    const [flashcardEditando, setFlashcardEditando] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showEditModal, setShowEditModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showDeleteConfirm, setShowDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para eliminación de colecciones\n    const [showDeleteCollectionConfirm, setShowDeleteCollectionConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingCollectionId, setDeletingCollectionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Estado para estadísticas\n    const [mostrarEstadisticasGenerales, setMostrarEstadisticasGenerales] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mostrarEstadisticasDetalladas, setMostrarEstadisticasDetalladas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Cargar colecciones al montar el componente\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FlashcardViewer.useEffect\": ()=>{\n            const cargarColecciones = {\n                \"FlashcardViewer.useEffect.cargarColecciones\": async ()=>{\n                    setIsLoading(true);\n                    try {\n                        const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerColeccionesFlashcards)();\n                        setColecciones(data);\n                    } catch (error) {\n                        console.error('Error al cargar colecciones:', error);\n                        setError('No se pudieron cargar las colecciones de flashcards');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"FlashcardViewer.useEffect.cargarColecciones\"];\n            cargarColecciones();\n        }\n    }[\"FlashcardViewer.useEffect\"], []);\n    // Manejar la selección de una colección\n    const handleSeleccionarColeccion = async (coleccion)=>{\n        setIsLoading(true);\n        setError('');\n        setColeccionSeleccionada(coleccion);\n        setActiveIndex(0);\n        setMostrarRespuesta(false);\n        setRespondiendo(false);\n        setModoEstudio(false);\n        try {\n            // Cargar flashcards con su progreso\n            const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccion.id);\n            // Ordenar las flashcards: primero las que deben estudiarse hoy, luego el resto\n            const ordenadas = [\n                ...data\n            ].sort((a, b)=>{\n                if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                return 0;\n            });\n            setFlashcards(ordenadas);\n            // Cargar estadísticas\n            const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccion.id);\n            setEstadisticas(stats);\n        } catch (error) {\n            console.error('Error al cargar flashcards:', error);\n            setError('No se pudieron cargar las flashcards de esta colección');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Iniciar el modo de estudio\n    const iniciarModoEstudio = async ()=>{\n        setIsLoading(true);\n        try {\n            if (coleccionSeleccionada) {\n                // Recargar las flashcards para asegurarnos de tener los datos más recientes\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                // Filtrar solo las flashcards que deben estudiarse hoy\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                // Verificar si el número de flashcards para estudiar coincide con las estadísticas\n                if (flashcardsParaEstudiar.length !== stats.paraHoy) {\n                    console.warn(\"Discrepancia en el conteo: \".concat(flashcardsParaEstudiar.length, \" flashcards filtradas vs \").concat(stats.paraHoy, \" en estad\\xedsticas\"));\n                }\n                // Si no hay flashcards para hoy, mostrar un mensaje\n                if (flashcardsParaEstudiar.length === 0) {\n                    if (data.length === 0) {\n                        alert('No hay flashcards en esta colección.');\n                    } else {\n                        alert('No hay flashcards programadas para estudiar hoy. Vuelve mañana o ajusta el progreso de las tarjetas.');\n                    }\n                    return; // Salir sin iniciar el modo estudio\n                }\n                // Usar solo las flashcards programadas para hoy\n                setFlashcards(flashcardsParaEstudiar);\n                // Iniciar el modo de estudio\n                setModoEstudio(true);\n                setActiveIndex(0);\n                setMostrarRespuesta(false);\n                setRespondiendo(false);\n            }\n        } catch (error) {\n            console.error('Error al iniciar modo de estudio:', error);\n            setError('No se pudo iniciar el modo de estudio');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Manejar la navegación entre flashcards\n    const handleNavigate = (direction)=>{\n        if (direction === 'prev' && activeIndex > 0) {\n            setActiveIndex(activeIndex - 1);\n            setMostrarRespuesta(false);\n        } else if (direction === 'next' && activeIndex < flashcards.length - 1) {\n            setActiveIndex(activeIndex + 1);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Manejar la respuesta a una flashcard\n    const handleRespuesta = async (dificultad)=>{\n        if (!coleccionSeleccionada || flashcards.length === 0) return;\n        const flashcardId = flashcards[activeIndex].id;\n        setRespondiendo(true);\n        try {\n            // Registrar la respuesta\n            await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.registrarRespuestaFlashcard)(flashcardId, dificultad);\n            // Recargar las flashcards y estadísticas si estamos en la última tarjeta\n            if (activeIndex >= flashcards.length - 1 && coleccionSeleccionada) {\n                const data = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerFlashcardsParaEstudiar)(coleccionSeleccionada.id);\n                const flashcardsParaEstudiar = data.filter((flashcard)=>flashcard.debeEstudiar);\n                // Actualizar estadísticas\n                const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                setEstadisticas(stats);\n                if (flashcardsParaEstudiar.length > 0) {\n                    setFlashcards(flashcardsParaEstudiar);\n                    setActiveIndex(0);\n                } else {\n                    // Si no hay más flashcards para hoy, mostrar mensaje y salir del modo de estudio\n                    alert('¡Has completado todas las flashcards para hoy! Vuelve mañana para continuar estudiando.');\n                    setModoEstudio(false);\n                    // Ordenar las flashcards: primero las que deben estudiarse (aunque ya no haya ninguna), luego el resto\n                    const ordenadas = [\n                        ...data\n                    ].sort((a, b)=>{\n                        if (a.debeEstudiar && !b.debeEstudiar) return -1;\n                        if (!a.debeEstudiar && b.debeEstudiar) return 1;\n                        return 0;\n                    });\n                    setFlashcards(ordenadas);\n                }\n            } else {\n                // Avanzar a la siguiente flashcard\n                handleNavigate('next');\n            }\n        } catch (error) {\n            console.error('Error al registrar respuesta:', error);\n            setError('No se pudo registrar la respuesta');\n        } finally{\n            setRespondiendo(false);\n            setMostrarRespuesta(false);\n        }\n    };\n    // Salir del modo de estudio\n    const handleSalirModoEstudio = ()=>{\n        setModoEstudio(false);\n    };\n    // Manejar la edición de una flashcard\n    const handleEditarFlashcard = (flashcard)=>{\n        setFlashcardEditando(flashcard);\n        setShowEditModal(true);\n    };\n    // Manejar el guardado de una flashcard editada\n    const handleGuardarFlashcard = (flashcardActualizada)=>{\n        // Actualizar la flashcard en la lista local\n        setFlashcards((prev)=>prev.map((fc)=>fc.id === flashcardActualizada.id ? flashcardActualizada : fc));\n    };\n    // Manejar la eliminación de una flashcard\n    const handleEliminarFlashcard = async (flashcardId)=>{\n        setDeletingId(flashcardId);\n        let loadingToastId;\n        try {\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].loading('Eliminando flashcard...');\n            const success = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarFlashcard)(flashcardId);\n            if (success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success('Flashcard eliminada exitosamente', {\n                    id: loadingToastId\n                });\n                // Actualizar la lista local\n                setFlashcards((prev)=>prev.filter((fc)=>fc.id !== flashcardId));\n                // Recargar estadísticas\n                if (coleccionSeleccionada) {\n                    const stats = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.obtenerEstadisticasColeccion)(coleccionSeleccionada.id);\n                    setEstadisticas(stats);\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error('Error al eliminar la flashcard', {\n                    id: loadingToastId\n                });\n            }\n        } catch (error) {\n            console.error('Error al eliminar flashcard:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error('Error al eliminar la flashcard', {\n                id: loadingToastId\n            });\n        } finally{\n            setDeletingId(null);\n            setShowDeleteConfirm(null);\n        }\n    };\n    // Manejar la eliminación de una colección completa\n    const handleEliminarColeccion = (coleccionId)=>{\n        setShowDeleteCollectionConfirm(coleccionId);\n    };\n    const confirmarEliminarColeccion = async (coleccionId)=>{\n        setDeletingCollectionId(coleccionId);\n        let loadingToastId;\n        try {\n            loadingToastId = react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].loading('Eliminando colección...');\n            const success = await (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.eliminarColeccionFlashcards)(coleccionId);\n            if (success) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success('Colección eliminada exitosamente', {\n                    id: loadingToastId\n                });\n                // Actualizar la lista local de colecciones\n                setColecciones((prev)=>prev.filter((col)=>col.id !== coleccionId));\n                // Si la colección eliminada era la seleccionada, limpiar la selección\n                if ((coleccionSeleccionada === null || coleccionSeleccionada === void 0 ? void 0 : coleccionSeleccionada.id) === coleccionId) {\n                    setColeccionSeleccionada(null);\n                    setFlashcards([]);\n                    setEstadisticas(null);\n                }\n            } else {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error('Error al eliminar la colección', {\n                    id: loadingToastId\n                });\n            }\n        } catch (error) {\n            console.error('Error al eliminar colección:', error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error('Error al eliminar la colección', {\n                id: loadingToastId\n            });\n        } finally{\n            setDeletingCollectionId(null);\n            setShowDeleteCollectionConfirm(null);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-4\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\",\n                children: error\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 313,\n                columnNumber: 9\n            }, undefined),\n            modoEstudio ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStudyMode__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                flashcards: flashcards,\n                activeIndex: activeIndex,\n                respondiendo: respondiendo,\n                onRespuesta: handleRespuesta,\n                onNavigate: handleNavigate,\n                onVolver: handleSalirModoEstudio\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 319,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Mis Flashcards\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setMostrarEstadisticasGenerales(true),\n                                className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiBarChart2_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiBarChart2, {\n                                        className: \"mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \" Estad\\xedsticas Generales\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, undefined),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center h-40\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 13\n                    }, undefined),\n                    !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardCollectionList__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        colecciones: colecciones,\n                        coleccionSeleccionada: coleccionSeleccionada,\n                        onSeleccionarColeccion: handleSeleccionarColeccion,\n                        onEliminarColeccion: handleEliminarColeccion,\n                        isLoading: false,\n                        deletingId: deletingCollectionId\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 348,\n                        columnNumber: 13\n                    }, undefined),\n                    !isLoading && colecciones.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 text-yellow-800 p-4 rounded flex items-start\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiBarChart2_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiAlertTriangle, {\n                                className: \"mr-2 mt-1 flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-medium\",\n                                        children: \"No hay colecciones de flashcards disponibles\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-1\",\n                                        children: 'Crea nuevas colecciones desde la secci\\xf3n \"Generar Flashcards\".'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 13\n                    }, undefined),\n                    coleccionSeleccionada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: coleccionSeleccionada.titulo\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardStatistics__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                estadisticas: estadisticas\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: iniciarModoEstudio,\n                                            className: \"bg-orange-500 hover:bg-orange-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Estudiar (\".concat(estadisticas ? estadisticas.paraHoy : 0, \" para hoy)\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setMostrarEstadisticasDetalladas(true),\n                                            className: \"bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline\",\n                                            children: \"Ver estad\\xedsticas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 15\n                            }, undefined),\n                            isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center items-center h-40\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 17\n                            }, undefined) : flashcards.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"No hay flashcards en esta colecci\\xf3n.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: flashcards.map((flashcard, index)=>{\n                                    var _flashcard_progreso, _flashcard_progreso1;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border rounded-lg p-4 \".concat(flashcard.debeEstudiar ? 'border-orange-300 bg-orange-50' : 'border-gray-200'),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Tarjeta \",\n                                                            index + 1\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            ((_flashcard_progreso = flashcard.progreso) === null || _flashcard_progreso === void 0 ? void 0 : _flashcard_progreso.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs \".concat(flashcard.progreso.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' : flashcard.progreso.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' : flashcard.progreso.estado === 'repasando' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'),\n                                                                children: flashcard.progreso.estado\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            !((_flashcard_progreso1 = flashcard.progreso) === null || _flashcard_progreso1 === void 0 ? void 0 : _flashcard_progreso1.estado) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"nuevo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                lineNumber: 432,\n                                                                columnNumber: 29\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleEditarFlashcard(flashcard),\n                                                                        className: \"p-1 text-blue-500 hover:bg-blue-50 rounded transition-colors\",\n                                                                        title: \"Editar flashcard\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiBarChart2_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiEdit2, {\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                            lineNumber: 444,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                        lineNumber: 439,\n                                                                        columnNumber: 29\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setShowDeleteConfirm(flashcard.id),\n                                                                        disabled: deletingId === flashcard.id,\n                                                                        className: \"p-1 text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50\",\n                                                                        title: \"Eliminar flashcard\",\n                                                                        children: deletingId === flashcard.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiBarChart2_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiRefreshCw, {\n                                                                            size: 14,\n                                                                            className: \"animate-spin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                            lineNumber: 453,\n                                                                            columnNumber: 33\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiBarChart2_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiTrash2, {\n                                                                            size: 14\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                            lineNumber: 455,\n                                                                            columnNumber: 33\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                        lineNumber: 446,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: flashcard.pregunta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 461,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-700 line-clamp-2\",\n                                                children: flashcard.respuesta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, flashcard.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 21\n                                    }, undefined);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 17\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, undefined),\n            flashcardEditando && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardEditModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                flashcard: flashcardEditando,\n                isOpen: showEditModal,\n                onClose: ()=>{\n                    setShowEditModal(false);\n                    setFlashcardEditando(null);\n                },\n                onSave: handleGuardarFlashcard\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 474,\n                columnNumber: 9\n            }, undefined),\n            showDeleteConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiBarChart2_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiAlertTriangle, {\n                                    className: \"text-red-500 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Confirmar eliminaci\\xf3n\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"\\xbfEst\\xe1s seguro de que quieres eliminar esta flashcard? Esta acci\\xf3n no se puede deshacer y se perder\\xe1 todo el progreso asociado.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowDeleteConfirm(null),\n                                    className: \"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\",\n                                    disabled: deletingId !== null,\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleEliminarFlashcard(showDeleteConfirm),\n                                    className: \"px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors\",\n                                    disabled: deletingId !== null,\n                                    children: \"Eliminar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 498,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 487,\n                columnNumber: 9\n            }, undefined),\n            showDeleteCollectionConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg p-6 max-w-md w-full mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiAlertTriangle_FiBarChart2_FiEdit2_FiRefreshCw_FiTrash2_react_icons_fi__WEBPACK_IMPORTED_MODULE_10__.FiAlertTriangle, {\n                                    className: \"text-red-500 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold\",\n                                    children: \"Confirmar eliminaci\\xf3n de colecci\\xf3n\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mb-6\",\n                            children: \"\\xbfEst\\xe1s seguro de que quieres eliminar esta colecci\\xf3n completa? Esta acci\\xf3n no se puede deshacer y se eliminar\\xe1n:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"text-gray-600 mb-6 list-disc list-inside space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Todas las flashcards de la colecci\\xf3n\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Todo el progreso de estudio\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"El historial de revisiones\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Las estad\\xedsticas asociadas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 535,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowDeleteCollectionConfirm(null),\n                                    className: \"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\",\n                                    disabled: deletingCollectionId !== null,\n                                    children: \"Cancelar\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>confirmarEliminarColeccion(showDeleteCollectionConfirm),\n                                    className: \"px-4 py-2 bg-red-500 text-white hover:bg-red-600 rounded-lg transition-colors\",\n                                    disabled: deletingCollectionId !== null,\n                                    children: \"Eliminar colecci\\xf3n\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 520,\n                columnNumber: 9\n            }, undefined),\n            mostrarEstadisticasGenerales && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardGeneralStatistics__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                onClose: ()=>setMostrarEstadisticasGenerales(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 560,\n                columnNumber: 9\n            }, undefined),\n            mostrarEstadisticasDetalladas && coleccionSeleccionada && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_FlashcardDetailedStatistics__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                coleccionId: coleccionSeleccionada.id,\n                coleccionTitulo: coleccionSeleccionada.titulo,\n                onClose: ()=>setMostrarEstadisticasDetalladas(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n                lineNumber: 567,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\components\\\\flashcards\\\\FlashcardViewer.tsx\",\n        lineNumber: 311,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FlashcardViewer, \"BGiUidYJ+/s7bjL0P3hDflRbjK4=\");\n_c = FlashcardViewer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FlashcardViewer);\nvar _c;\n$RefreshReg$(_c, \"FlashcardViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/flashcards/FlashcardViewer.tsx\n"));

/***/ })

});