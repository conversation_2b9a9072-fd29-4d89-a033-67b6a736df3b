import React from 'react';
import { FiX, <PERSON><PERSON><PERSON><PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON>, FiX as FiXI<PERSON>, FiTrendingUp, FiAward } from 'react-icons/fi';

interface EstadisticasGeneralesTests {
  totalTests: number;
  totalRespuestasCorrectas: number;
  totalRespuestasIncorrectas: number;
  porcentajeAcierto: number;
}

interface TestGeneralStatisticsProps {
  estadisticas: EstadisticasGeneralesTests;
  onClose: () => void;
}

const TestGeneralStatistics: React.FC<TestGeneralStatisticsProps> = ({
  estadisticas,
  onClose
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">Estadísticas Generales de Tests</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
          >
            <FiX size={24} />
          </button>
        </div>

        {/* Estadísticas principales */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiAward className="text-blue-600 mr-2 text-xl" />
              <h4 className="font-semibold">Tests Realizados</h4>
            </div>
            <p className="text-3xl font-bold text-blue-700">{estadisticas.totalTests}</p>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiCheck className="text-green-600 mr-2 text-xl" />
              <h4 className="font-semibold">Respuestas Correctas</h4>
            </div>
            <p className="text-3xl font-bold text-green-700">{estadisticas.totalRespuestasCorrectas}</p>
          </div>

          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiXIcon className="text-red-600 mr-2 text-xl" />
              <h4 className="font-semibold">Respuestas Incorrectas</h4>
            </div>
            <p className="text-3xl font-bold text-red-700">{estadisticas.totalRespuestasIncorrectas}</p>
          </div>

          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiTrendingUp className="text-purple-600 mr-2 text-xl" />
              <h4 className="font-semibold">Porcentaje de Acierto</h4>
            </div>
            <p className="text-3xl font-bold text-purple-700">{estadisticas.porcentajeAcierto.toFixed(1)}%</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestGeneralStatistics;
