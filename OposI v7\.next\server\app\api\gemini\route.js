/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/gemini/route";
exports.ids = ["app/api/gemini/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_OposI_v7_src_app_api_gemini_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/gemini/route.ts */ \"(rsc)/./src/app/api/gemini/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/gemini/route\",\n        pathname: \"/api/gemini\",\n        filename: \"route\",\n        bundlePath: \"app/api/gemini/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\OposI v7\\\\src\\\\app\\\\api\\\\gemini\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_OposI_v7_src_app_api_gemini_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/gemini/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/gemini/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_gemini__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gemini */ \"(rsc)/./src/lib/gemini.ts\");\n/* harmony import */ var _lib_gemini_questionService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/gemini/questionService */ \"(rsc)/./src/lib/gemini/questionService.ts\");\n/* harmony import */ var _lib_zodSchemas__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/zodSchemas */ \"(rsc)/./src/lib/zodSchemas.ts\");\n\n\n\n\n\n// API route for Gemini actions\nasync function POST(req) {\n    try {\n        // Crear cliente de Supabase usando la implementación correcta\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabaseClient)();\n        const { data: { user }, error: userError } = await supabase.auth.getUser();\n        // Diagnóstico mejorado para debugging\n        console.log('User check:', {\n            hasUser: !!user,\n            userError: userError?.message,\n            userId: user?.id,\n            cookies: req.headers.get('cookie')?.includes('supabase') ? 'present' : 'missing'\n        });\n        if (!user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized',\n                debug: {\n                    userError: userError?.message,\n                    hasCookies: !!req.headers.get('cookie')\n                }\n            }, {\n                status: 401\n            });\n        }\n        const body = await req.json();\n        // Validación robusta de entrada\n        const parseResult = _lib_zodSchemas__WEBPACK_IMPORTED_MODULE_4__.ApiGeminiInputSchema.safeParse(body);\n        if (!parseResult.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Datos inválidos',\n                detalles: parseResult.error.errors\n            }, {\n                status: 400\n            });\n        }\n        // Compatibilidad: si viene pregunta+documentos, es para obtenerRespuestaIA\n        if (body.pregunta && body.documentos) {\n            const result = await (0,_lib_gemini_questionService__WEBPACK_IMPORTED_MODULE_3__.obtenerRespuestaIA)(body.pregunta, body.documentos);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                result\n            });\n        }\n        const { action, peticion, contextos } = body;\n        let result;\n        switch(action){\n            case 'generarTest':\n                result = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_2__.generarTest)(peticion, contextos);\n                break;\n            case 'generarFlashcards':\n                result = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_2__.generarFlashcards)(peticion, contextos);\n                break;\n            case 'generarMapaMental':\n                result = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_2__.generarMapaMental)(peticion, contextos);\n                break;\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Acción no soportada'\n                }, {\n                    status: 400\n                });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            result\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9nZW1pbmkvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQXdEO0FBQ1c7QUFDYztBQUNmO0FBQ1Y7QUFFeEQsK0JBQStCO0FBQ3hCLGVBQWVPLEtBQUtDLEdBQWdCO0lBQ3pDLElBQUk7UUFDRiw4REFBOEQ7UUFDOUQsTUFBTUMsV0FBVyxNQUFNUixnRkFBMEJBO1FBRWpELE1BQU0sRUFBRVMsTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRUMsT0FBT0MsU0FBUyxFQUFFLEdBQUcsTUFBTUosU0FBU0ssSUFBSSxDQUFDQyxPQUFPO1FBRXhFLHNDQUFzQztRQUN0Q0MsUUFBUUMsR0FBRyxDQUFDLGVBQWU7WUFDekJDLFNBQVMsQ0FBQyxDQUFDUDtZQUNYRSxXQUFXQSxXQUFXTTtZQUN0QkMsUUFBUVQsTUFBTVU7WUFDZEMsU0FBU2QsSUFBSWUsT0FBTyxDQUFDQyxHQUFHLENBQUMsV0FBV0MsU0FBUyxjQUFjLFlBQVk7UUFDekU7UUFFQSxJQUFJLENBQUNkLE1BQU07WUFDVCxPQUFPWCxxREFBWUEsQ0FBQzBCLElBQUksQ0FBQztnQkFDdkJkLE9BQU87Z0JBQ1BlLE9BQU87b0JBQ0xkLFdBQVdBLFdBQVdNO29CQUN0QlMsWUFBWSxDQUFDLENBQUNwQixJQUFJZSxPQUFPLENBQUNDLEdBQUcsQ0FBQztnQkFDaEM7WUFDRixHQUFHO2dCQUFFSyxRQUFRO1lBQUk7UUFDbkI7UUFFQSxNQUFNQyxPQUFPLE1BQU10QixJQUFJa0IsSUFBSTtRQUUzQixnQ0FBZ0M7UUFDaEMsTUFBTUssY0FBY3pCLGlFQUFvQkEsQ0FBQzBCLFNBQVMsQ0FBQ0Y7UUFDbkQsSUFBSSxDQUFDQyxZQUFZRSxPQUFPLEVBQUU7WUFDeEIsT0FBT2pDLHFEQUFZQSxDQUFDMEIsSUFBSSxDQUFDO2dCQUN2QmQsT0FBTztnQkFDUHNCLFVBQVVILFlBQVluQixLQUFLLENBQUN1QixNQUFNO1lBQ3BDLEdBQUc7Z0JBQUVOLFFBQVE7WUFBSTtRQUNuQjtRQUNBLDJFQUEyRTtRQUMzRSxJQUFJQyxLQUFLTSxRQUFRLElBQUlOLEtBQUtPLFVBQVUsRUFBRTtZQUNwQyxNQUFNQyxTQUFTLE1BQU1qQywrRUFBa0JBLENBQUN5QixLQUFLTSxRQUFRLEVBQUVOLEtBQUtPLFVBQVU7WUFDdEUsT0FBT3JDLHFEQUFZQSxDQUFDMEIsSUFBSSxDQUFDO2dCQUFFWTtZQUFPO1FBQ3BDO1FBQ0EsTUFBTSxFQUFFQyxNQUFNLEVBQUVDLFFBQVEsRUFBRUMsU0FBUyxFQUFFLEdBQUdYO1FBQ3hDLElBQUlRO1FBQ0osT0FBUUM7WUFDTixLQUFLO2dCQUNIRCxTQUFTLE1BQU1wQyx3REFBV0EsQ0FBQ3NDLFVBQVVDO2dCQUNyQztZQUNGLEtBQUs7Z0JBQ0hILFNBQVMsTUFBTW5DLDhEQUFpQkEsQ0FBQ3FDLFVBQVVDO2dCQUMzQztZQUNGLEtBQUs7Z0JBQ0hILFNBQVMsTUFBTWxDLDhEQUFpQkEsQ0FBQ29DLFVBQVVDO2dCQUMzQztZQUNGO2dCQUNFLE9BQU96QyxxREFBWUEsQ0FBQzBCLElBQUksQ0FBQztvQkFBRWQsT0FBTztnQkFBc0IsR0FBRztvQkFBRWlCLFFBQVE7Z0JBQUk7UUFDN0U7UUFDQSxPQUFPN0IscURBQVlBLENBQUMwQixJQUFJLENBQUM7WUFBRVk7UUFBTztJQUNwQyxFQUFFLE9BQU8xQixPQUFPO1FBQ2QsT0FBT1oscURBQVlBLENBQUMwQixJQUFJLENBQUM7WUFBRWQsT0FBTyxNQUFpQk8sT0FBTztRQUFDLEdBQUc7WUFBRVUsUUFBUTtRQUFJO0lBQzlFO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbmFhdGFcXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcT3Bvc0lcXE9wb3NJIHY3XFxzcmNcXGFwcFxcYXBpXFxnZW1pbmlcXHJvdXRlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgeyBjcmVhdGVTZXJ2ZXJTdXBhYmFzZUNsaWVudCB9IGZyb20gJ0AvbGliL3N1cGFiYXNlL3NlcnZlcic7XG5pbXBvcnQgeyBnZW5lcmFyVGVzdCwgZ2VuZXJhckZsYXNoY2FyZHMsIGdlbmVyYXJNYXBhTWVudGFsIH0gZnJvbSAnQC9saWIvZ2VtaW5pJztcbmltcG9ydCB7IG9idGVuZXJSZXNwdWVzdGFJQSB9IGZyb20gJ0AvbGliL2dlbWluaS9xdWVzdGlvblNlcnZpY2UnO1xuaW1wb3J0IHsgQXBpR2VtaW5pSW5wdXRTY2hlbWEgfSBmcm9tICdAL2xpYi96b2RTY2hlbWFzJztcblxuLy8gQVBJIHJvdXRlIGZvciBHZW1pbmkgYWN0aW9uc1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxOiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIC8vIENyZWFyIGNsaWVudGUgZGUgU3VwYWJhc2UgdXNhbmRvIGxhIGltcGxlbWVudGFjacOzbiBjb3JyZWN0YVxuICAgIGNvbnN0IHN1cGFiYXNlID0gYXdhaXQgY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQoKTtcblxuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0sIGVycm9yOiB1c2VyRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpO1xuXG4gICAgLy8gRGlhZ27Ds3N0aWNvIG1lam9yYWRvIHBhcmEgZGVidWdnaW5nXG4gICAgY29uc29sZS5sb2coJ1VzZXIgY2hlY2s6Jywge1xuICAgICAgaGFzVXNlcjogISF1c2VyLFxuICAgICAgdXNlckVycm9yOiB1c2VyRXJyb3I/Lm1lc3NhZ2UsXG4gICAgICB1c2VySWQ6IHVzZXI/LmlkLFxuICAgICAgY29va2llczogcmVxLmhlYWRlcnMuZ2V0KCdjb29raWUnKT8uaW5jbHVkZXMoJ3N1cGFiYXNlJykgPyAncHJlc2VudCcgOiAnbWlzc2luZydcbiAgICB9KTtcblxuICAgIGlmICghdXNlcikge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgZXJyb3I6ICdVbmF1dGhvcml6ZWQnLFxuICAgICAgICBkZWJ1Zzoge1xuICAgICAgICAgIHVzZXJFcnJvcjogdXNlckVycm9yPy5tZXNzYWdlLFxuICAgICAgICAgIGhhc0Nvb2tpZXM6ICEhcmVxLmhlYWRlcnMuZ2V0KCdjb29raWUnKVxuICAgICAgICB9XG4gICAgICB9LCB7IHN0YXR1czogNDAxIH0pO1xuICAgIH1cblxuICAgIGNvbnN0IGJvZHkgPSBhd2FpdCByZXEuanNvbigpO1xuXG4gICAgLy8gVmFsaWRhY2nDs24gcm9idXN0YSBkZSBlbnRyYWRhXG4gICAgY29uc3QgcGFyc2VSZXN1bHQgPSBBcGlHZW1pbmlJbnB1dFNjaGVtYS5zYWZlUGFyc2UoYm9keSk7XG4gICAgaWYgKCFwYXJzZVJlc3VsdC5zdWNjZXNzKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBlcnJvcjogJ0RhdG9zIGludsOhbGlkb3MnLFxuICAgICAgICBkZXRhbGxlczogcGFyc2VSZXN1bHQuZXJyb3IuZXJyb3JzXG4gICAgICB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgIH1cbiAgICAvLyBDb21wYXRpYmlsaWRhZDogc2kgdmllbmUgcHJlZ3VudGErZG9jdW1lbnRvcywgZXMgcGFyYSBvYnRlbmVyUmVzcHVlc3RhSUFcbiAgICBpZiAoYm9keS5wcmVndW50YSAmJiBib2R5LmRvY3VtZW50b3MpIHtcbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IG9idGVuZXJSZXNwdWVzdGFJQShib2R5LnByZWd1bnRhLCBib2R5LmRvY3VtZW50b3MpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgcmVzdWx0IH0pO1xuICAgIH1cbiAgICBjb25zdCB7IGFjdGlvbiwgcGV0aWNpb24sIGNvbnRleHRvcyB9ID0gYm9keTtcbiAgICBsZXQgcmVzdWx0O1xuICAgIHN3aXRjaCAoYWN0aW9uKSB7XG4gICAgICBjYXNlICdnZW5lcmFyVGVzdCc6XG4gICAgICAgIHJlc3VsdCA9IGF3YWl0IGdlbmVyYXJUZXN0KHBldGljaW9uLCBjb250ZXh0b3MpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ2dlbmVyYXJGbGFzaGNhcmRzJzpcbiAgICAgICAgcmVzdWx0ID0gYXdhaXQgZ2VuZXJhckZsYXNoY2FyZHMocGV0aWNpb24sIGNvbnRleHRvcyk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnZ2VuZXJhck1hcGFNZW50YWwnOlxuICAgICAgICByZXN1bHQgPSBhd2FpdCBnZW5lcmFyTWFwYU1lbnRhbChwZXRpY2lvbiwgY29udGV4dG9zKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ0FjY2nDs24gbm8gc29wb3J0YWRhJyB9LCB7IHN0YXR1czogNDAwIH0pO1xuICAgIH1cbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyByZXN1bHQgfSk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHsgZXJyb3I6IChlcnJvciBhcyBFcnJvcikubWVzc2FnZSB9LCB7IHN0YXR1czogNTAwIH0pO1xuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiY3JlYXRlU2VydmVyU3VwYWJhc2VDbGllbnQiLCJnZW5lcmFyVGVzdCIsImdlbmVyYXJGbGFzaGNhcmRzIiwiZ2VuZXJhck1hcGFNZW50YWwiLCJvYnRlbmVyUmVzcHVlc3RhSUEiLCJBcGlHZW1pbmlJbnB1dFNjaGVtYSIsIlBPU1QiLCJyZXEiLCJzdXBhYmFzZSIsImRhdGEiLCJ1c2VyIiwiZXJyb3IiLCJ1c2VyRXJyb3IiLCJhdXRoIiwiZ2V0VXNlciIsImNvbnNvbGUiLCJsb2ciLCJoYXNVc2VyIiwibWVzc2FnZSIsInVzZXJJZCIsImlkIiwiY29va2llcyIsImhlYWRlcnMiLCJnZXQiLCJpbmNsdWRlcyIsImpzb24iLCJkZWJ1ZyIsImhhc0Nvb2tpZXMiLCJzdGF0dXMiLCJib2R5IiwicGFyc2VSZXN1bHQiLCJzYWZlUGFyc2UiLCJzdWNjZXNzIiwiZGV0YWxsZXMiLCJlcnJvcnMiLCJwcmVndW50YSIsImRvY3VtZW50b3MiLCJyZXN1bHQiLCJhY3Rpb24iLCJwZXRpY2lvbiIsImNvbnRleHRvcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/gemini/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/prompts.ts":
/*!*******************************!*\
  !*** ./src/config/prompts.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROMPT_FLASHCARDS: () => (/* binding */ PROMPT_FLASHCARDS),\n/* harmony export */   PROMPT_MAPAS_MENTALES: () => (/* binding */ PROMPT_MAPAS_MENTALES),\n/* harmony export */   PROMPT_PREGUNTAS: () => (/* binding */ PROMPT_PREGUNTAS),\n/* harmony export */   PROMPT_TESTS: () => (/* binding */ PROMPT_TESTS)\n/* harmony export */ });\n/**\n * Configuración de prompts personalizados para cada funcionalidad de la aplicación\n *\n * Este archivo centraliza todos los prompts que se utilizan en la aplicación,\n * permitiendo personalizarlos fácilmente sin tener que modificar el código de los servicios.\n */ /**\n * Prompt para la pantalla de preguntas y respuestas\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {pregunta}: Pregunta del usuario\n */ const PROMPT_PREGUNTAS = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado y con amplia experiencia. Tu misión principal es ayudar al usuario a comprender a fondo los temas del temario, resolver sus dudas y, en última instancia, maximizar sus posibilidades de obtener una plaza. Tu tono debe ser profesional, claro, didáctico, motivador y empático.\n\nResponde SIEMPRE en español.\n\nCONTEXTO DEL TEMARIO (Información base para tus explicaciones):\n{documentos}\n\nPREGUNTA DEL OPOSITOR/A:\n{pregunta}\n\nINSTRUCCIONES DETALLADAS PARA ACTUAR COMO \"MENTOR OPOSITOR AI\":\n\nI. PRINCIPIOS GENERALES DE RESPUESTA:\n\n1.  Adaptabilidad de la Extensión y Tono Inicial:\n    -   Inicio de Respuesta: Ve al grano. No es necesario comenzar cada respuesta con frases como \"¡Excelente pregunta!\". Puedes usar una frase validando la pregunta o mostrando empatía de forma ocasional y variada, solo si realmente aporta valor o la pregunta es particularmente compleja o bien formulada. En la mayoría de los casos, es mejor empezar directamente con la información solicitada.\n    -   Preguntas Específicas sobre Contenido: Si la pregunta es sobre un concepto, definición, detalle del temario, o pide una explicación profunda de una sección, puedes extenderte para asegurar una comprensión completa, siempre basándote en el CONTEXTO.\n    -   Preguntas sobre Estructura, Planificación o Consejos Generales: Si la pregunta es sobre cómo abordar el estudio de un tema, cuáles son sus apartados principales, o pide consejos generales, sé estratégico y conciso. Evita resumir todo el contenido del tema. Céntrate en el método, la estructura o los puntos clave de forma resumida.\n    -   Claridad ante Todo: Independientemente de la extensión, la claridad y la precisión son primordiales.\n\n2.  Respuesta Basada en el Contexto (Precisión Absoluta):\n    -   Tu respuesta DEBE basarse ESTRICTA y ÚNICAMENTE en la información proporcionada en el \"CONTEXTO DEL TEMARIO\".\n    -   Si la información necesaria no está en el contexto, indícalo claramente (e.g., \"El temario que me has proporcionado aborda X de esta manera... Para un detalle más exhaustivo sobre Y, sería necesario consultar fuentes complementarias.\"). NO INVENTES INFORMACIÓN.\n    -   Cita textualmente partes relevantes del contexto solo cuando sea indispensable para la precisión o para ilustrar un punto crucial, introduciéndolas de forma natural.\n\nII. FORMATO DE LISTAS JERÁRQUICAS (CUANDO APLIQUE):\nAl presentar información estructurada, como los apartados de un tema, utiliza el siguiente formato de lista jerárquica ESTRICTO:\nEjemplo de formato:\n1.  Apartado Principal Uno\n    a)  Subapartado Nivel 1\n        -   Elemento Nivel 2 (con un guion y espacio)\n            *   Detalle Nivel 3 (con un asterisco y espacio)\n    b)  Otro Subapartado Nivel 1\n2.  Apartado Principal Dos\n    a)  Subapartado...\n\n-   Utiliza números seguidos de un punto (1., 2.) para el nivel más alto.\n-   Utiliza letras minúsculas seguidas de un paréntesis (a), b)) para el segundo nivel, indentado.\n-   Utiliza un guion seguido de un espacio ('- ') para el tercer nivel, indentado bajo el anterior.\n-   Utiliza un asterisco seguido de un espacio ('* ') para el cuarto nivel (o niveles subsiguientes), indentado bajo el anterior.\n-   Asegúrate de que la indentación sea clara para reflejar la jerarquía.\n-   NO uses formato markdown de énfasis (como dobles asteriscos) para los títulos de los elementos de la lista en TU SALIDA; la propia estructura jerárquica y la numeración/viñeta son suficientes.\n\nIII. TIPOS DE RESPUESTA Y ENFOQUES ESPECÍFICOS:\n\nA.  Si la PREGUNTA es sobre \"CUÁLES SON LOS APARTADOS DE UN TEMA\" o \"ESTRUCTURA DEL TEMA\":\n    -   Formato de Respuesta: Utiliza el FORMATO DE LISTAS JERÁRQUICAS detallado en la sección II.\n    -   Contenido por Elemento de Lista:\n        1.  Apartados Principales (Nivel 1 - Números): Indica su título exacto o una paráfrasis muy fiel. A continuación, en 1-2 frases concisas, describe su propósito general.\n        2.  Subapartados (Nivel 2 - Letras): Solo el título o idea principal en muy pocas palabras.\n        3.  Niveles Inferiores (Guion, Asterisco): Solo el título o idea principal en muy pocas palabras.\n    -   El objetivo es mostrar la ESTRUCTURA, no detallar el contenido aquí.\n    -   Sugerencia General de Abordaje (Opcional y Muy Breve al final): Puedes añadir una frase sugiriendo un orden de estudio.\n    -   Qué EVITAR: Descripciones largas del contenido de cada elemento de la lista. Párrafos extensos dentro de la lista.\n\nB.  Si la PREGUNTA es sobre CÓMO ESTUDIAR UN TEMA (enfoque metodológico):\n    -   Enfoque Estratégico y Conciso:\n        1.  Visión General Breve.\n        2.  Para cada bloque principal del tema (puedes usar el Nivel 1 del formato de lista): Indica brevemente su objetivo (1-2 frases) y sugiere 1-2 acciones o técnicas de estudio clave y concretas.\n        3.  Menciona 2-3 Puntos Transversales Críticos (si los hay).\n        4.  Consejo General Final.\n    -   Qué EVITAR: Resumir detalladamente el contenido al explicar la técnica. Uso excesivo de énfasis.\n\nC.  Si la PREGUNTA es sobre un CONCEPTO ESPECÍFICO, DETALLE DEL TEMARIO o PIDE UNA EXPLICACIÓN PROFUNDA:\n    -   Enfoque Explicativo y Didáctico (Puedes Extenderte):\n        (Mantener las sub-instrucciones de explicación detallada: Definición, Terminología, Relevancia, Puntos Clave, Ejemplos, Conexiones).\n        -   Si necesitas desglosar una explicación en múltiples puntos, puedes usar el FORMATO DE LISTAS JERÁRQUICAS de la sección II.\n\nIV. ESTILO Y CIERRE (PARA TODAS LAS RESPUESTAS):\n\n1.  Claridad y Estructura: Utiliza párrafos bien definidos. Cuando uses listas, sigue el formato especificado.\n2.  Tono: Profesional, didáctico, paciente, motivador y positivo. Sé directo y ve al grano, especialmente al inicio de la respuesta.\n3.  Cierre:\n    -   Finaliza ofreciendo más ayuda o preguntando si la explicación ha sido clara (e.g., \"¿Queda clara la estructura así?\", \"¿Necesitas que profundicemos en algún punto de estos apartados?\").\n    -   Termina con una frase de ánimo variada y natural, no siempre la misma.\n\nPRIORIDAD MÁXIMA: La exactitud basada en el CONTEXTO es innegociable. La adaptabilidad en la extensión y el formato deben servir para mejorar la claridad y utilidad de la respuesta, no para introducir información no contextual.\n\n`;\n/**\n * Prompt para la generación de flashcards\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {cantidad}: Número de flashcards a generar\n * - {instrucciones}: Instrucciones adicionales (opcional)\n */ const PROMPT_FLASHCARDS = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un conjunto de flashcards (tarjetas de estudio) basadas en el contenido proporcionado. Estas flashcards serán utilizadas por un estudiante para repasar conceptos clave.\n\nCONTEXTO DEL TEMARIO (Información base para tus flashcards):\n{documentos}\n\nPETICIÓN DEL USUARIO:\nGenera {cantidad} flashcards de alta calidad.\n{instrucciones}\n\nINSTRUCCIONES PARA CREAR FLASHCARDS:\n\n1. Genera entre 5 y 15 flashcards de alta calidad basadas ÚNICAMENTE en la información proporcionada en el CONTEXTO DEL TEMARIO.\n2. Cada flashcard debe tener:\n   - Una pregunta clara y concisa en el anverso\n   - Una respuesta completa pero concisa en el reverso\n3. Las preguntas deben ser variadas e incluir:\n   - Definiciones de conceptos clave\n   - Relaciones entre conceptos\n   - Aplicaciones prácticas\n   - Clasificaciones o categorías\n4. Las respuestas deben:\n   - Ser precisas y basadas estrictamente en el contenido del CONTEXTO\n   - Incluir la información esencial sin ser excesivamente largas\n   - Estar redactadas de forma clara y didáctica\n5. NO inventes información que no esté en el CONTEXTO.\n6. Responde SIEMPRE en español.\n\nFORMATO DE RESPUESTA:\nDebes proporcionar tu respuesta en formato JSON, con un array de objetos donde cada objeto representa una flashcard con las propiedades \"pregunta\" y \"respuesta\". Ejemplo:\n\n[\n  {\n    \"pregunta\": \"¿Qué es X concepto?\",\n    \"respuesta\": \"X concepto es...\"\n  },\n  {\n    \"pregunta\": \"Enumera las características principales de Y\",\n    \"respuesta\": \"Las características principales de Y son: 1)..., 2)..., 3)...\"\n  }\n]\n\nIMPORTANTE: Tu respuesta debe contener ÚNICAMENTE el array JSON, sin texto adicional antes o después. No incluyas marcadores de código ni la palabra json antes del array.\n`;\n/**\n * Prompt para la generación de mapas mentales\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {instrucciones}: Instrucciones adicionales (opcional)\n */ const PROMPT_MAPAS_MENTALES = `\n\"Eres 'Mentor Opositor AI', un experto en visualización de información y preparador de oposiciones. Tu tarea es generar un mapa mental interactivo en formato HTML basado en el contenido y las directrices que te proporcionaré.\n\nAquí tienes la información con la que trabajarás:\n\nCONTEXTO DEL TEMARIO (Información base para tu mapa mental):\n{documentos}\n\nPETICIÓN DEL USUARIO (Tema principal, enfoque y detalles deseados para el mapa):\n{instrucciones}\n\nCrea un mapa mental digital en código html, css y javascript. Tu respuesta debe ser ÚNICAMENTE ese archivo HTML completo, sin ningún texto explicativo antes ni después.\n\nEste mapa mental debe:\n\nOrganizar la Información: Basándote en el CONTEXTO DEL TEMARIO y mi PETICIÓN DEL USUARIO, identifica el concepto central que será la raíz del mapa. Luego, estructura las ideas principales que se derivan de él, y de estas, las ideas secundarias o más detalladas, formando una jerarquía clara como un árbol.\n\nSer Interactivo:\n\nDebo poder hacer clic en las ideas (nodos) para mostrar u ocultar las sub-ideas conectadas a ellas. Así, puedo empezar con una vista general y explorar los detalles a mi ritmo.\n\nDebo poder acercarme o alejarme (hacer zoom) y desplazarme por el mapa (hacer panorámica) con facilidad para navegar por toda la información.\n\nTener un Diseño Claro y Funcional:\n\nCada idea (nodo) debe representarse como un rectángulo.\n\nEl texto dentro de cada rectángulo debe ajustarse automáticamente si es largo, sin cortarse y haciendo que el rectángulo se adapte a su contenido para que todo sea legible.\n\nSi una idea tiene una explicación adicional breve (sacada del CONTEXTO DEL TEMARIO o mi PETICIÓN), me gustaría que apareciera una pequeña descripción al pasar el ratón por encima del nodo correspondiente.\n\nSería ideal que los diferentes niveles de ideas (el tema central, las ideas principales, las secundarias) tuvieran un estilo visual ligeramente distinto (quizás usando colores suaves y profesionales) para diferenciarlos fácilmente.\n\nLas líneas que conectan las ideas deben ser claras y conectar los rectángulos de forma ordenada.\n\nPresentación Inicial:\n\nCuando abra el mapa por primera vez, debería estar bien centrado en la pantalla y, preferiblemente, no mostrar todas las ramas expandidas de golpe, sino quizás solo los niveles más importantes, para luego yo poder explorar.\n\nRobustez:\n\nEs crucial que el mapa mental funcione fluidamente, sin errores, y que la información se presente de manera lógica y correcta. Asegúrate de que todas las partes del mapa se carguen y se muestren correctamente.\n\nEn resumen: analiza el material proporcionado, extrae la jerarquía de conceptos según mis indicaciones, y genera un único archivo HTML que contenga este mapa mental interactivo, navegable, visualmente claro y funcional. Recuerda, solo el código HTML.\n`;\n/**\n * Prompt para la generación de tests\n *\n * Variables disponibles:\n * - {documentos}: Contenido de los documentos seleccionados\n * - {cantidad}: Número de preguntas a generar\n * - {instrucciones}: Instrucciones adicionales (opcional)\n */ const PROMPT_TESTS = `\nEres \"Mentor Opositor AI\", un preparador de oposiciones virtual altamente cualificado. Tu tarea es crear un conjunto de preguntas de test de opción múltiple (4 opciones, 1 correcta) basadas en el contenido proporcionado. Estas preguntas serán utilizadas por un estudiante para evaluar su comprensión del temario.\n\nCONTEXTO DEL TEMARIO (Información base para tus preguntas):\n{documentos}\n\nPETICIÓN DEL USUARIO:\nGenera {cantidad} preguntas de test de alta calidad.\nInstrucciones específicas del usuario: {instrucciones}\n\nINSTRUCCIONES PARA CREAR PREGUNTAS DE TEST:\n\n1.  Genera EXACTAMENTE la {cantidad}, que solicite el usuario, de preguntas de test de alta calidad.\n2.  BASA TODAS las preguntas y opciones de respuesta ESTRICTA y ÚNICAMENTE en la información proporcionada en el \"CONTEXTO DEL TEMARIO\".\n3.  ENFOCA cada pregunta según las \"Instrucciones específicas del usuario\" ({instrucciones}). Si las instrucciones piden centrarse en \"artículos, sus números y su contenido\", entonces CADA pregunta debe tratar directamente sobre:\n    a)  El número de un artículo específico y lo que establece.\n    b)  El contenido principal de un artículo específico, preguntando a qué artículo pertenece o detalles clave.\n    c)  La relación entre un concepto y el artículo que lo regula.\n    EVITA preguntas generales sobre historia, contexto de aprobación de leyes, o interpretaciones amplias a menos que las \"Instrucciones específicas del usuario\" ({instrucciones}) lo indiquen explícitamente.\n4.  Cada objeto de pregunta en el array JSON resultante debe tener las siguientes propiedades DIRECTAS:\n    -   \"pregunta\": (string) El texto de la pregunta.\n    -   \"opcion_a\": (string) El texto para la opción A.\n    -   \"opcion_b\": (string) El texto para la opción B.\n    -   \"opcion_c\": (string) El texto para la opción C.\n    -   \"opcion_d\": (string) El texto para la opción D.\n    -   \"respuesta_correcta\": (string) Debe ser 'a', 'b', 'c', o 'd', indicando cuál de las opciones es la correcta.\n    NO anides las opciones (opcion_a, opcion_b, etc.) dentro de otro objeto llamado \"opciones\". Deben ser propiedades directas del objeto de la pregunta.\n5.  Las preguntas deben ser claras, concisas y evaluar la comprensión de conceptos clave, detalles importantes, relaciones, etc., SIEMPRE dentro del enfoque solicitado en {instrucciones}.\n6.  Las opciones de respuesta deben ser plausibles y estar basadas en el contexto, pero solo una debe ser inequívocamente correcta según el temario proporcionado y el enfoque de la pregunta.\n7.  NO inventes información que no esté en el CONTEXTO.\n8.  Responde SIEMPRE en español.\n\nFORMATO DE RESPUESTA:\nDebes proporcionar tu respuesta en formato JSON, con un array de objetos donde cada objeto representa una pregunta con las propiedades directas \"pregunta\", \"opcion_a\", \"opcion_b\", \"opcion_c\", \"opcion_d\" y \"respuesta_correcta\". Ejemplo:\n\n[\n  {\n    \"pregunta\": \"¿Qué establece el Artículo X de la Ley Y sobre Z?\",\n    \"opcion_a\": \"Opción A relacionada con el artículo X\",\n    \"opcion_b\": \"Opción B relacionada con el artículo X (correcta)\",\n    \"opcion_c\": \"Opción C relacionada con el artículo X\",\n    \"opcion_d\": \"Opción D relacionada con el artículo X\",\n    \"respuesta_correcta\": \"b\"\n  },\n  {\n    \"pregunta\": \"El concepto de [concepto clave] se regula principalmente en el artículo:\",\n    \"opcion_a\": \"Artículo A\",\n    \"opcion_b\": \"Artículo B\",\n    \"opcion_c\": \"Artículo C (correcta)\",\n    \"opcion_d\": \"Artículo D\",\n    \"respuesta_correcta\": \"c\"\n  }\n]\n\nIMPORTANTE: Tu respuesta debe contener ÚNICAMENTE el array JSON, sin texto adicional antes o después. No incluyas marcadores de código ni la palabra json antes del array.\n`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/prompts.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini.ts":
/*!***************************!*\
  !*** ./src/lib/gemini.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genAI: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.genAI),\n/* harmony export */   generarFlashcards: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.generarFlashcards),\n/* harmony export */   generarMapaMental: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.generarMapaMental),\n/* harmony export */   generarTest: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.generarTest),\n/* harmony export */   model: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.model),\n/* harmony export */   obtenerRespuestaIA: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.obtenerRespuestaIA),\n/* harmony export */   prepararDocumentos: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos),\n/* harmony export */   truncarContenido: () => (/* reexport safe */ _gemini_index__WEBPACK_IMPORTED_MODULE_0__.truncarContenido)\n/* harmony export */ });\n/* harmony import */ var _gemini_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./gemini/index */ \"(rsc)/./src/lib/gemini/index.ts\");\n// Este archivo es un punto de entrada para mantener la compatibilidad con el código existente\n// Redirige todas las exportaciones a la nueva estructura modular\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2dlbWluaS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQSw4RkFBOEY7QUFDOUYsaUVBQWlFO0FBRWxDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxsaWJcXGdlbWluaS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBFc3RlIGFyY2hpdm8gZXMgdW4gcHVudG8gZGUgZW50cmFkYSBwYXJhIG1hbnRlbmVyIGxhIGNvbXBhdGliaWxpZGFkIGNvbiBlbCBjw7NkaWdvIGV4aXN0ZW50ZVxuLy8gUmVkaXJpZ2UgdG9kYXMgbGFzIGV4cG9ydGFjaW9uZXMgYSBsYSBudWV2YSBlc3RydWN0dXJhIG1vZHVsYXJcblxuZXhwb3J0ICogZnJvbSBcIi4vZ2VtaW5pL2luZGV4XCI7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/flashcardGenerator.ts":
/*!**********************************************!*\
  !*** ./src/lib/gemini/flashcardGenerator.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarFlashcards: () => (/* binding */ generarFlashcards)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Genera flashcards a partir de los documentos\n */ async function generarFlashcards(documentos, cantidad = 10, instrucciones) {\n    try {\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            throw new Error(\"No se han proporcionado documentos para generar flashcards.\");\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        let prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_FLASHCARDS.replace('{documentos}', contenidoDocumentos).replace('{cantidad}', cantidad.toString());\n        // Añadir instrucciones adicionales si se proporcionan\n        if (instrucciones) {\n            prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\\n- ${instrucciones}`);\n        } else {\n            prompt = prompt.replace('{instrucciones}', '');\n        }\n        // Generar las flashcards\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response.text();\n        // Extraer el JSON de la respuesta\n        const jsonMatch = response.match(/\\[\\s*\\{[\\s\\S]*\\}\\s*\\]/);\n        if (!jsonMatch) {\n            throw new Error(\"No se pudo extraer el formato JSON de la respuesta.\");\n        }\n        const flashcardsJson = jsonMatch[0];\n        const flashcards = JSON.parse(flashcardsJson);\n        // Validar el formato\n        if (!Array.isArray(flashcards) || flashcards.length === 0) {\n            throw new Error(\"El formato de las flashcards generadas no es válido.\");\n        }\n        return flashcards;\n    } catch (error) {\n        console.error('Error al generar flashcards:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2dlbWluaS9mbGFzaGNhcmRHZW5lcmF0b3IudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTJEO0FBQ0Y7QUFFekQ7O0NBRUMsR0FDTSxlQUFlRyxrQkFDcEJDLFVBQTZGLEVBQzdGQyxXQUFtQixFQUFFLEVBQ3JCQyxhQUFzQjtJQUV0QixJQUFJO1FBQ0YsMENBQTBDO1FBQzFDLE1BQU1DLHNCQUFzQk4saUVBQWtCQSxDQUFDRztRQUUvQyxJQUFJLENBQUNHLHFCQUFxQjtZQUN4QixNQUFNLElBQUlDLE1BQU07UUFDbEI7UUFFQSxnRUFBZ0U7UUFDaEUsd0NBQXdDO1FBQ3hDLElBQUlDLFNBQVNQLDhEQUFpQkEsQ0FDM0JRLE9BQU8sQ0FBQyxnQkFBZ0JILHFCQUN4QkcsT0FBTyxDQUFDLGNBQWNMLFNBQVNNLFFBQVE7UUFFMUMsc0RBQXNEO1FBQ3RELElBQUlMLGVBQWU7WUFDakJHLFNBQVNBLE9BQU9DLE9BQU8sQ0FBQyxtQkFBbUIsQ0FBQyw4QkFBOEIsRUFBRUosZUFBZTtRQUM3RixPQUFPO1lBQ0xHLFNBQVNBLE9BQU9DLE9BQU8sQ0FBQyxtQkFBbUI7UUFDN0M7UUFFQSx5QkFBeUI7UUFDekIsTUFBTUUsU0FBUyxNQUFNWixnREFBS0EsQ0FBQ2EsZUFBZSxDQUFDSjtRQUMzQyxNQUFNSyxXQUFXRixPQUFPRSxRQUFRLENBQUNDLElBQUk7UUFFckMsa0NBQWtDO1FBQ2xDLE1BQU1DLFlBQVlGLFNBQVNHLEtBQUssQ0FBQztRQUVqQyxJQUFJLENBQUNELFdBQVc7WUFDZCxNQUFNLElBQUlSLE1BQU07UUFDbEI7UUFFQSxNQUFNVSxpQkFBaUJGLFNBQVMsQ0FBQyxFQUFFO1FBQ25DLE1BQU1HLGFBQWFDLEtBQUtDLEtBQUssQ0FBQ0g7UUFFOUIscUJBQXFCO1FBQ3JCLElBQUksQ0FBQ0ksTUFBTUMsT0FBTyxDQUFDSixlQUFlQSxXQUFXSyxNQUFNLEtBQUssR0FBRztZQUN6RCxNQUFNLElBQUloQixNQUFNO1FBQ2xCO1FBRUEsT0FBT1c7SUFDVCxFQUFFLE9BQU9NLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDOUMsTUFBTUE7SUFDUjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFxPcG9zSSB2N1xcc3JjXFxsaWJcXGdlbWluaVxcZmxhc2hjYXJkR2VuZXJhdG9yLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG1vZGVsLCBwcmVwYXJhckRvY3VtZW50b3MgfSBmcm9tICcuL2dlbWluaUNsaWVudCc7XG5pbXBvcnQgeyBQUk9NUFRfRkxBU0hDQVJEUyB9IGZyb20gJy4uLy4uL2NvbmZpZy9wcm9tcHRzJztcblxuLyoqXG4gKiBHZW5lcmEgZmxhc2hjYXJkcyBhIHBhcnRpciBkZSBsb3MgZG9jdW1lbnRvc1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2VuZXJhckZsYXNoY2FyZHMoXG4gIGRvY3VtZW50b3M6IHsgdGl0dWxvOiBzdHJpbmc7IGNvbnRlbmlkbzogc3RyaW5nOyBjYXRlZ29yaWE/OiBzdHJpbmc7IG51bWVyb190ZW1hPzogbnVtYmVyIH1bXSxcbiAgY2FudGlkYWQ6IG51bWJlciA9IDEwLFxuICBpbnN0cnVjY2lvbmVzPzogc3RyaW5nXG4pOiBQcm9taXNlPHsgcHJlZ3VudGE6IHN0cmluZzsgcmVzcHVlc3RhOiBzdHJpbmcgfVtdPiB7XG4gIHRyeSB7XG4gICAgLy8gUHJlcGFyYXIgZWwgY29udGVuaWRvIGRlIGxvcyBkb2N1bWVudG9zXG4gICAgY29uc3QgY29udGVuaWRvRG9jdW1lbnRvcyA9IHByZXBhcmFyRG9jdW1lbnRvcyhkb2N1bWVudG9zKTtcblxuICAgIGlmICghY29udGVuaWRvRG9jdW1lbnRvcykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiTm8gc2UgaGFuIHByb3BvcmNpb25hZG8gZG9jdW1lbnRvcyBwYXJhIGdlbmVyYXIgZmxhc2hjYXJkcy5cIik7XG4gICAgfVxuXG4gICAgLy8gQ29uc3RydWlyIGVsIHByb21wdCBwYXJhIGxhIElBIHVzYW5kbyBlbCBwcm9tcHQgcGVyc29uYWxpemFkb1xuICAgIC8vIFJlZW1wbGF6YXIgbGFzIHZhcmlhYmxlcyBlbiBlbCBwcm9tcHRcbiAgICBsZXQgcHJvbXB0ID0gUFJPTVBUX0ZMQVNIQ0FSRFNcbiAgICAgIC5yZXBsYWNlKCd7ZG9jdW1lbnRvc30nLCBjb250ZW5pZG9Eb2N1bWVudG9zKVxuICAgICAgLnJlcGxhY2UoJ3tjYW50aWRhZH0nLCBjYW50aWRhZC50b1N0cmluZygpKTtcblxuICAgIC8vIEHDsWFkaXIgaW5zdHJ1Y2Npb25lcyBhZGljaW9uYWxlcyBzaSBzZSBwcm9wb3JjaW9uYW5cbiAgICBpZiAoaW5zdHJ1Y2Npb25lcykge1xuICAgICAgcHJvbXB0ID0gcHJvbXB0LnJlcGxhY2UoJ3tpbnN0cnVjY2lvbmVzfScsIGBJbnN0cnVjY2lvbmVzIGFkaWNpb25hbGVzOlxcbi0gJHtpbnN0cnVjY2lvbmVzfWApO1xuICAgIH0gZWxzZSB7XG4gICAgICBwcm9tcHQgPSBwcm9tcHQucmVwbGFjZSgne2luc3RydWNjaW9uZXN9JywgJycpO1xuICAgIH1cblxuICAgIC8vIEdlbmVyYXIgbGFzIGZsYXNoY2FyZHNcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBtb2RlbC5nZW5lcmF0ZUNvbnRlbnQocHJvbXB0KTtcbiAgICBjb25zdCByZXNwb25zZSA9IHJlc3VsdC5yZXNwb25zZS50ZXh0KCk7XG5cbiAgICAvLyBFeHRyYWVyIGVsIEpTT04gZGUgbGEgcmVzcHVlc3RhXG4gICAgY29uc3QganNvbk1hdGNoID0gcmVzcG9uc2UubWF0Y2goL1xcW1xccypcXHtbXFxzXFxTXSpcXH1cXHMqXFxdLyk7XG5cbiAgICBpZiAoIWpzb25NYXRjaCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiTm8gc2UgcHVkbyBleHRyYWVyIGVsIGZvcm1hdG8gSlNPTiBkZSBsYSByZXNwdWVzdGEuXCIpO1xuICAgIH1cblxuICAgIGNvbnN0IGZsYXNoY2FyZHNKc29uID0ganNvbk1hdGNoWzBdO1xuICAgIGNvbnN0IGZsYXNoY2FyZHMgPSBKU09OLnBhcnNlKGZsYXNoY2FyZHNKc29uKTtcblxuICAgIC8vIFZhbGlkYXIgZWwgZm9ybWF0b1xuICAgIGlmICghQXJyYXkuaXNBcnJheShmbGFzaGNhcmRzKSB8fCBmbGFzaGNhcmRzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRWwgZm9ybWF0byBkZSBsYXMgZmxhc2hjYXJkcyBnZW5lcmFkYXMgbm8gZXMgdsOhbGlkby5cIik7XG4gICAgfVxuXG4gICAgcmV0dXJuIGZsYXNoY2FyZHM7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgYWwgZ2VuZXJhciBmbGFzaGNhcmRzOicsIGVycm9yKTtcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIm1vZGVsIiwicHJlcGFyYXJEb2N1bWVudG9zIiwiUFJPTVBUX0ZMQVNIQ0FSRFMiLCJnZW5lcmFyRmxhc2hjYXJkcyIsImRvY3VtZW50b3MiLCJjYW50aWRhZCIsImluc3RydWNjaW9uZXMiLCJjb250ZW5pZG9Eb2N1bWVudG9zIiwiRXJyb3IiLCJwcm9tcHQiLCJyZXBsYWNlIiwidG9TdHJpbmciLCJyZXN1bHQiLCJnZW5lcmF0ZUNvbnRlbnQiLCJyZXNwb25zZSIsInRleHQiLCJqc29uTWF0Y2giLCJtYXRjaCIsImZsYXNoY2FyZHNKc29uIiwiZmxhc2hjYXJkcyIsIkpTT04iLCJwYXJzZSIsIkFycmF5IiwiaXNBcnJheSIsImxlbmd0aCIsImVycm9yIiwiY29uc29sZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/flashcardGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/geminiClient.ts":
/*!****************************************!*\
  !*** ./src/lib/gemini/geminiClient.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genAI: () => (/* binding */ genAI),\n/* harmony export */   model: () => (/* binding */ model),\n/* harmony export */   prepararDocumentos: () => (/* binding */ prepararDocumentos),\n/* harmony export */   truncarContenido: () => (/* binding */ truncarContenido)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n// Configuración de la API de Gemini\nconst API_KEY = process.env.GEMINI_API_KEY || '';\nconst MODEL_NAME = 'gemini-2.5-flash-preview-05-20';\n// Verificar que la API key esté configurada\nif (!API_KEY) {\n    console.error('GEMINI_API_KEY no está configurada en las variables de entorno');\n}\n// Inicializar el cliente de Gemini\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(API_KEY);\nconst model = genAI.getGenerativeModel({\n    model: MODEL_NAME,\n    safetySettings: [\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_HARASSMENT,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        },\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_HATE_SPEECH,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        },\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        },\n        {\n            category: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,\n            threshold: _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE\n        }\n    ]\n});\n// Ya no usamos instrucciones base globales, cada servicio usa su propio prompt personalizado\n/**\n * Trunca el contenido de un documento si es demasiado largo\n */ function truncarContenido(contenido, maxLength = 25000) {\n    // Verificar que el contenido sea una cadena válida\n    if (contenido === undefined || contenido === null) {\n        console.warn('Se intentó truncar un contenido undefined o null');\n        return '';\n    }\n    // Asegurarse de que el contenido sea una cadena\n    const contenidoStr = String(contenido);\n    if (contenidoStr.length <= maxLength) {\n        return contenidoStr;\n    }\n    return contenidoStr.substring(0, maxLength) + `\\n\\n[CONTENIDO TRUNCADO: El documento original es más largo. Esta es una versión reducida para procesamiento.]`;\n}\n// --- Nueva lógica de Chunking ---\nconst CHUNK_SIZE = 5000; // Caracteres\nconst CHUNK_OVERLAP = 200; // Caracteres\nconst MAX_TOTAL_CONTEXT_LENGTH = 50000; // Caracteres\nfunction createTextChunks(documentTitle, content) {\n    if (!content) {\n        return [];\n    }\n    const contentStr = String(content);\n    const chunks = [];\n    let chunkIndex = 0;\n    let currentIndex = 0;\n    while(currentIndex < contentStr.length){\n        const endIndex = Math.min(currentIndex + CHUNK_SIZE, contentStr.length);\n        const text = contentStr.substring(currentIndex, endIndex);\n        chunks.push({\n            originalDocumentTitle: documentTitle,\n            chunkIndex: chunkIndex + 1,\n            text\n        });\n        chunkIndex++;\n        if (endIndex === contentStr.length) {\n            break;\n        }\n        currentIndex += CHUNK_SIZE - CHUNK_OVERLAP;\n        // Asegurar que no haya un bucle infinito si CHUNK_OVERLAP >= CHUNK_SIZE\n        if (currentIndex >= endIndex && endIndex < contentStr.length) {\n            currentIndex = endIndex; // Avanzar al menos hasta el final del chunk actual\n        }\n    }\n    return chunks;\n}\n/**\n * Prepara los documentos para enviarlos al modelo, dividiéndolos en chunks.\n */ function prepararDocumentos(documentos) {\n    if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {\n        console.warn('No se proporcionaron documentos válidos para prepararDocumentos');\n        return '';\n    }\n    try {\n        const allChunks = [];\n        for (const doc of documentos){\n            if (!doc || typeof doc !== 'object' || !doc.titulo || doc.contenido === undefined || doc.contenido === null) {\n                console.warn('Documento inválido, sin título o contenido en prepararDocumentos:', doc);\n                continue;\n            }\n            const fullOriginalTitle = `${doc.categoria ? `[${doc.categoria}] ` : ''}${doc.numero_tema ? `Tema ${doc.numero_tema}: ` : ''}${doc.titulo}`;\n            const documentChunks = createTextChunks(fullOriginalTitle.trim(), doc.contenido);\n            allChunks.push(...documentChunks);\n        }\n        if (allChunks.length === 0) {\n            console.warn('No se generaron chunks a partir de los documentos proporcionados.');\n            return '';\n        }\n        let fullContext = allChunks.map((chunk)=>{\n            return `\n=== INICIO CHUNK: ${chunk.originalDocumentTitle} - Parte ${chunk.chunkIndex} ===\n${chunk.text}\n=== FIN CHUNK: ${chunk.originalDocumentTitle} - Parte ${chunk.chunkIndex} ===\n`;\n        }).join('\\n\\n');\n        if (fullContext.length > MAX_TOTAL_CONTEXT_LENGTH) {\n            console.warn(`El contexto combinado (${fullContext.length} caracteres) excede el máximo de ${MAX_TOTAL_CONTEXT_LENGTH}. Se truncará.`);\n            fullContext = fullContext.substring(0, MAX_TOTAL_CONTEXT_LENGTH) + `\\n\\n[CONTEXTO GENERAL TRUNCADO: El contenido combinado de todos los chunks excedió el límite máximo (${MAX_TOTAL_CONTEXT_LENGTH} caracteres).]`;\n        }\n        return fullContext;\n    } catch (error) {\n        console.error('Error al preparar documentos con chunks:', error);\n        return '';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/geminiClient.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/index.ts":
/*!*********************************!*\
  !*** ./src/lib/gemini/index.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   genAI: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.genAI),\n/* harmony export */   generarFlashcards: () => (/* binding */ generarFlashcards),\n/* harmony export */   generarMapaMental: () => (/* binding */ generarMapaMental),\n/* harmony export */   generarTest: () => (/* binding */ generarTest),\n/* harmony export */   model: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model),\n/* harmony export */   obtenerRespuestaIA: () => (/* reexport safe */ _questionService__WEBPACK_IMPORTED_MODULE_1__.obtenerRespuestaIA),\n/* harmony export */   prepararDocumentos: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos),\n/* harmony export */   truncarContenido: () => (/* reexport safe */ _geminiClient__WEBPACK_IMPORTED_MODULE_0__.truncarContenido)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _questionService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./questionService */ \"(rsc)/./src/lib/gemini/questionService.ts\");\n/* harmony import */ var _flashcardGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./flashcardGenerator */ \"(rsc)/./src/lib/gemini/flashcardGenerator.ts\");\n/* harmony import */ var _testGenerator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./testGenerator */ \"(rsc)/./src/lib/gemini/testGenerator.ts\");\n/* harmony import */ var _mindMapGenerator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./mindMapGenerator */ \"(rsc)/./src/lib/gemini/mindMapGenerator.ts\");\n// Exportar todo desde los archivos individuales\n\n\n\n\n\n// Función adaptadora para compatibilidad con la interfaz anterior de flashcards\nasync function generarFlashcards(peticion, contextos) {\n    // Convertir los contextos al formato esperado por la función original\n    const documentos = contextos.map((contenido, index)=>({\n            titulo: `Documento ${index + 1}`,\n            contenido\n        }));\n    // Llamar a la función original con los documentos formateados y la petición como instrucción\n    return await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./flashcardGenerator */ \"(rsc)/./src/lib/gemini/flashcardGenerator.ts\")).then((module)=>module.generarFlashcards(documentos, 10, peticion));\n}\n// Función adaptadora para compatibilidad con la interfaz anterior de mapas mentales\nasync function generarMapaMental(peticion, contextos) {\n    // Convertir los contextos al formato esperado por la función original\n    const documentos = contextos.map((contenido, index)=>({\n            titulo: `Documento ${index + 1}`,\n            contenido\n        }));\n    // Llamar a la función original con los documentos formateados y la petición como instrucción\n    return await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./mindMapGenerator */ \"(rsc)/./src/lib/gemini/mindMapGenerator.ts\")).then((module)=>module.generarMapaMental(documentos, peticion));\n}\n// Función adaptadora para compatibilidad con la interfaz anterior de tests\nasync function generarTest(peticion, contextos) {\n    // Convertir los contextos al formato esperado por la función original\n    const documentos = contextos.map((contenido, index)=>({\n            titulo: `Documento ${index + 1}`,\n            contenido\n        }));\n    // Llamar a la función original con los documentos formateados y la petición como instrucción\n    const result = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! ./testGenerator */ \"(rsc)/./src/lib/gemini/testGenerator.ts\")).then((module)=>module.generarTest(documentos, 10, peticion));\n    // Convertir el formato de la respuesta al formato esperado por el componente\n    return result.map((item)=>({\n            pregunta: item.pregunta,\n            opciones: {\n                a: item.opcion_a,\n                b: item.opcion_b,\n                c: item.opcion_c,\n                d: item.opcion_d\n            },\n            respuesta_correcta: item.respuesta_correcta\n        }));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/mindMapGenerator.ts":
/*!********************************************!*\
  !*** ./src/lib/gemini/mindMapGenerator.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarMapaMental: () => (/* binding */ generarMapaMental)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Genera un mapa mental a partir de los documentos\n */ async function generarMapaMental(documentos, instrucciones) {\n    try {\n        // Validar entrada\n        if (!documentos || documentos.length === 0) {\n            throw new Error(\"No se han proporcionado documentos para generar el mapa mental.\");\n        }\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos || contenidoDocumentos.trim().length === 0) {\n            throw new Error(\"El contenido de los documentos está vacío o no es válido.\");\n        }\n        // Validar y limpiar instrucciones\n        const instruccionesLimpias = instrucciones?.trim() || 'Crea un mapa mental que organice los conceptos principales del contenido.';\n        // Construir el prompt final con validación\n        let finalPrompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_MAPAS_MENTALES.replace('{documentos}', contenidoDocumentos);\n        finalPrompt = finalPrompt.replace('{instrucciones}', instruccionesLimpias);\n        // Generar el mapa mental con timeout\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(finalPrompt);\n        const response = result.response.text();\n        // Validar que la respuesta no esté vacía\n        if (!response || response.trim().length === 0) {\n            throw new Error(\"La IA no generó ningún contenido para el mapa mental.\");\n        }\n        // Extraer y validar el HTML completo de la respuesta\n        let htmlContent = response.trim();\n        // Buscar el HTML en la respuesta (puede estar envuelto en markdown)\n        const htmlMatch = htmlContent.match(/<!DOCTYPE html>[\\s\\S]*<\\/html>/i);\n        if (htmlMatch) {\n            htmlContent = htmlMatch[0];\n        }\n        // Verificar que la respuesta contiene HTML válido\n        if (!htmlContent.includes('<!DOCTYPE html>') && !htmlContent.includes('<html')) {\n            console.error('Respuesta de Gemini para mapa mental:', response.substring(0, 500) + '...');\n            throw new Error(\"La respuesta no contiene HTML válido para el mapa mental.\");\n        }\n        // Validaciones adicionales de contenido HTML\n        const requiredElements = [\n            {\n                name: 'SVG',\n                pattern: '<svg'\n            },\n            {\n                name: 'D3.js',\n                pattern: 'd3js.org/d3'\n            },\n            {\n                name: 'JavaScript functions',\n                pattern: 'function'\n            },\n            {\n                name: 'Tree data',\n                pattern: 'treeData'\n            }\n        ];\n        const missingElements = requiredElements.filter((element)=>!htmlContent.includes(element.pattern));\n        if (missingElements.length > 0) {\n            console.error('Elementos faltantes en el HTML:', missingElements.map((e)=>e.name));\n            console.error('Contenido HTML generado:', htmlContent.substring(0, 1000) + '...');\n            throw new Error(`El mapa mental generado no contiene elementos esenciales: ${missingElements.map((e)=>e.name).join(', ')}`);\n        }\n        // Limpiar el HTML de posibles caracteres problemáticos\n        htmlContent = htmlContent.replace(/```html/gi, '').replace(/```/g, '').trim();\n        return htmlContent;\n    } catch (error) {\n        console.error('Error al generar mapa mental:', error);\n        // Proporcionar un mensaje de error más descriptivo si es posible\n        if (error instanceof Error) {\n            throw new Error(`Error al generar el mapa mental: ${error.message}`);\n        }\n        throw new Error(\"Ha ocurrido un error inesperado al generar el mapa mental.\");\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/mindMapGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/questionService.ts":
/*!*******************************************!*\
  !*** ./src/lib/gemini/questionService.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   obtenerRespuestaIA: () => (/* binding */ obtenerRespuestaIA)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Obtiene una respuesta de la IA a una pregunta sobre los documentos\n */ async function obtenerRespuestaIA(pregunta, documentos) {\n    try {\n        // Verificar que la pregunta sea válida\n        if (!pregunta || typeof pregunta !== 'string' || pregunta.trim() === '') {\n            console.warn('Se recibió una pregunta vacía o inválida');\n            return \"Por favor, proporciona una pregunta válida.\";\n        }\n        // Verificar que los documentos sean válidos\n        if (!documentos || !Array.isArray(documentos) || documentos.length === 0) {\n            console.warn('No se proporcionaron documentos válidos para obtenerRespuestaIA');\n            return \"No se han proporcionado documentos para responder a esta pregunta.\";\n        }\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            console.warn('No se pudo preparar el contenido de los documentos');\n            return \"No se han podido procesar los documentos proporcionados. Por favor, verifica que los documentos contengan información válida.\";\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        const prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_PREGUNTAS.replace('{documentos}', contenidoDocumentos).replace('{pregunta}', pregunta);\n        // Generar la respuesta\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response;\n        return response.text();\n    } catch (error) {\n        console.error('Error al obtener respuesta de la IA:', error);\n        // Proporcionar un mensaje de error más descriptivo si es posible\n        if (error instanceof Error) {\n            return `Lo siento, ha ocurrido un error al procesar tu pregunta: ${error.message}. Por favor, inténtalo de nuevo más tarde.`;\n        }\n        return \"Lo siento, ha ocurrido un error al procesar tu pregunta. Por favor, inténtalo de nuevo más tarde.\";\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/questionService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/gemini/testGenerator.ts":
/*!*****************************************!*\
  !*** ./src/lib/gemini/testGenerator.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generarTest: () => (/* binding */ generarTest)\n/* harmony export */ });\n/* harmony import */ var _geminiClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./geminiClient */ \"(rsc)/./src/lib/gemini/geminiClient.ts\");\n/* harmony import */ var _config_prompts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../config/prompts */ \"(rsc)/./src/config/prompts.ts\");\n\n\n/**\n * Genera un test con preguntas de opción múltiple a partir de los documentos\n */ async function generarTest(documentos, cantidad = 10, instrucciones) {\n    try {\n        // Preparar el contenido de los documentos\n        const contenidoDocumentos = (0,_geminiClient__WEBPACK_IMPORTED_MODULE_0__.prepararDocumentos)(documentos);\n        if (!contenidoDocumentos) {\n            throw new Error(\"No se han proporcionado documentos para generar el test.\");\n        }\n        // Construir el prompt para la IA usando el prompt personalizado\n        // Reemplazar las variables en el prompt\n        let prompt = _config_prompts__WEBPACK_IMPORTED_MODULE_1__.PROMPT_TESTS.replace('{documentos}', contenidoDocumentos).replace('{cantidad}', cantidad.toString());\n        // Añadir instrucciones adicionales si se proporcionan\n        if (instrucciones) {\n            prompt = prompt.replace('{instrucciones}', `Instrucciones adicionales:\\n- ${instrucciones}`);\n        } else {\n            prompt = prompt.replace('{instrucciones}', '');\n        }\n        // Generar el test\n        const result = await _geminiClient__WEBPACK_IMPORTED_MODULE_0__.model.generateContent(prompt);\n        const response = result.response.text();\n        // Extraer el JSON de la respuesta\n        const jsonMatch = response.match(/\\[\\s*\\{[\\s\\S]*\\}\\s*\\]/);\n        if (!jsonMatch) {\n            throw new Error(\"No se pudo extraer el formato JSON de la respuesta.\");\n        }\n        const testJson = jsonMatch[0];\n        const preguntas = JSON.parse(testJson);\n        // Validar el formato\n        if (!Array.isArray(preguntas) || preguntas.length === 0) {\n            throw new Error(\"El formato de las preguntas generadas no es válido.\");\n        }\n        // Validar que cada pregunta tiene el formato correcto\n        preguntas.forEach((pregunta, index)=>{\n            if (!pregunta.pregunta || !pregunta.opcion_a || !pregunta.opcion_b || !pregunta.opcion_c || !pregunta.opcion_d || !pregunta.respuesta_correcta) {\n                throw new Error(`La pregunta ${index + 1} no tiene el formato correcto.`);\n            }\n            // Asegurarse de que la respuesta correcta es una de las opciones válidas\n            if (![\n                'a',\n                'b',\n                'c',\n                'd'\n            ].includes(pregunta.respuesta_correcta)) {\n                throw new Error(`La respuesta correcta de la pregunta ${index + 1} no es válida.`);\n            }\n        });\n        return preguntas;\n    } catch (error) {\n        console.error('Error al generar test:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/gemini/testGenerator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabaseClient: () => (/* binding */ createServerSupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// Cliente para el servidor (componentes del servidor, API routes)\nasync function createServerSupabaseClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ4bmhweGppamluZnV4eHhwbHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0OTE4MDQsImV4cCI6MjA2MzA2NzgwNH0.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/zodSchemas.ts":
/*!*******************************!*\
  !*** ./src/lib/zodSchemas.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiGeminiInputSchema: () => (/* binding */ ApiGeminiInputSchema),\n/* harmony export */   DocumentoSchema: () => (/* binding */ DocumentoSchema),\n/* harmony export */   GenerarFlashcardsSchema: () => (/* binding */ GenerarFlashcardsSchema),\n/* harmony export */   GenerarMapaMentalSchema: () => (/* binding */ GenerarMapaMentalSchema),\n/* harmony export */   GenerarTestSchema: () => (/* binding */ GenerarTestSchema),\n/* harmony export */   PreguntaSchema: () => (/* binding */ PreguntaSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n// Directorio para esquemas Zod reutilizables\n\nconst DocumentoSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    titulo: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(200),\n    contenido: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1),\n    categoria: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional().nullable(),\n    numero_tema: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int().positive(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.z[\"null\"](),\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.undefined()\n    ]).optional(),\n    creado_en: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    actualizado_en: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    user_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    tipo_original: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional()\n});\nconst PreguntaSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    pregunta: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    documentos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(DocumentoSchema).min(1)\n});\nconst GenerarTestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarTest'),\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    contextos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1))\n});\nconst GenerarFlashcardsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarFlashcards'),\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    contextos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1))\n});\nconst GenerarMapaMentalSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    action: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal('generarMapaMental'),\n    peticion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1).max(500),\n    contextos: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1))\n});\nconst ApiGeminiInputSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    PreguntaSchema,\n    GenerarTestSchema,\n    GenerarFlashcardsSchema,\n    GenerarMapaMentalSchema\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/zodSchemas.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/zod","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgemini%2Froute&page=%2Fapi%2Fgemini%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgemini%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5COposI%20v7&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();